<svg width="46" height="54" viewBox="0 0 46 54" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M38.989 6.68947C34.6754 2.37569 28.9398 0 22.8393 0C16.7388 0 11.0032 2.37569 6.68948 6.68947C2.37569 11.0034 0 16.7388 0 22.8392C0 35.1803 11.6689 45.4451 17.9378 50.9596C18.809 51.726 19.5613 52.3878 20.1607 52.9476C20.9117 53.6491 21.8755 53.9999 22.8392 53.9999C23.8031 53.9999 24.7668 53.6491 25.5178 52.9476C26.1172 52.3877 26.8695 51.726 27.7407 50.9596C34.0096 45.445 45.6785 35.1803 45.6785 22.8392C45.6784 16.7388 43.3028 11.0034 38.989 6.68947ZM25.6513 48.5847C24.7611 49.3678 23.9923 50.0442 23.3584 50.6362C23.0672 50.908 22.6112 50.9081 22.3199 50.6362C21.6861 50.044 20.9172 49.3677 20.027 48.5846C14.1334 43.4003 3.16302 33.75 3.16302 22.8393C3.16302 11.9899 11.9896 3.16333 22.8391 3.16333C33.6885 3.16333 42.5151 11.9899 42.5151 22.8393C42.5152 33.75 31.5449 43.4003 25.6513 48.5847Z" fill="#E5F346"/>
<circle cx="23" cy="23" r="14" fill="#E5F346"/>
<mask id="mask0_20_249" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="13" y="14" width="19" height="19">
<rect x="13" y="14" width="19" height="19" fill="url(#pattern0)"/>
</mask>
<g mask="url(#mask0_20_249)">
<rect x="13" y="14" width="19" height="19" fill="#1C1817"/>
</g>
<defs>
<pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_20_249" transform="scale(0.0078125)"/>
</pattern>
<image id="image0_20_249" width="128" height="128" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
