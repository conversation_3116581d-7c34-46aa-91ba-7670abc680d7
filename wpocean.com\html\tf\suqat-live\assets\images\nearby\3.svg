<svg width="46" height="54" viewBox="0 0 46 54" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M38.989 6.68947C34.6754 2.37569 28.9398 0 22.8393 0C16.7388 0 11.0032 2.37569 6.68948 6.68947C2.37569 11.0034 0 16.7388 0 22.8392C0 35.1803 11.6689 45.4451 17.9378 50.9596C18.809 51.726 19.5613 52.3878 20.1607 52.9476C20.9117 53.6491 21.8755 53.9999 22.8392 53.9999C23.8031 53.9999 24.7668 53.6491 25.5178 52.9476C26.1172 52.3877 26.8695 51.726 27.7407 50.9596C34.0096 45.445 45.6785 35.1803 45.6785 22.8392C45.6784 16.7388 43.3028 11.0034 38.989 6.68947ZM25.6513 48.5847C24.7611 49.3678 23.9923 50.0442 23.3584 50.6362C23.0672 50.908 22.6112 50.9081 22.3199 50.6362C21.6861 50.044 20.9172 49.3677 20.027 48.5846C14.1334 43.4003 3.16302 33.75 3.16302 22.8393C3.16302 11.9899 11.9896 3.16333 22.8391 3.16333C33.6885 3.16333 42.5151 11.9899 42.5151 22.8393C42.5152 33.75 31.5449 43.4003 25.6513 48.5847Z" fill="#E5F346"/>
<circle cx="23" cy="23" r="14" fill="#E5F346"/>
<mask id="mask0_20_237" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="13" y="14" width="19" height="19">
<rect x="13" y="14" width="19" height="19" fill="url(#pattern0)"/>
</mask>
<g mask="url(#mask0_20_237)">
<rect x="13" y="14" width="19" height="19" fill="#1C1817"/>
</g>
<defs>
<pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_20_237" transform="scale(0.0078125)"/>
</pattern>
<image id="image0_20_237" width="128" height="128" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
