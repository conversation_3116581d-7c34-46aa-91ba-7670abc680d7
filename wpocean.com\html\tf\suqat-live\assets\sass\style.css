/*------------------------------------------------------------------
[Master <PERSON>]
Theme Name:  Suqat- Real Estate Single Property Template
Version:        1.0.0
-------------------------------------------------------------------*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------

1. General
	1.1 Theme Reset Style
	1.2 Global Elements

2. header
	2.1 topbar
	2.2 navigation

3. content
	3.1 wpo-hero-slider
	3.2 wpo-about-section
	3.3 wpo-payment-section
	3.4 wpo-service-section
	3.5 wpo-plan-section
	3.6 wpo-property-section
	3.7 wpo-place-section
	3.8 wpo-team-section 
	3.9 wpo-testimonial-section
	3.10 wpo-blog-section

4. wpo-footer

5. Home-style-2
   5.1 features-content
   5.2 wpo-service-section-s2
   5.3 wpo-plan-section-s2
   5.4 wpo-place-section-s2
   5.5 wpo-payment-section-s2
   5.6 wpo-testimonial-section-s2
   5.7 wpo-faq-section
   5.8 wpo-blog-section-s2

6. About-Page

7. wpo-service-single

8. property-single-pages

9. wpo-team-single

10. wpo-shop-page

11. wpo-shop-single-page

12. wpo-cart-page-style

13. wpo-checkout-page-style

14. wpo-blog-pg-section

15. wpo-blog-single-section

16. wpo-contact-pg-section

17. wpo-faq-page

18. error-404-section


----------------------------------------------------------------*/
/*------------------------------------------------------------------
1. General
----------------------------------------------------------------*/
/*---------------------------
	Fonts
----------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Epilogue:wght@300;400;500;600;700&amp;family=Sora:wght@300;400;500;600;700;800&amp;display=swap");
/* 1.1 Theme Reset Style */
html {
  font-size: 15px;
}

:root {
  scroll-behavior: unset;
}

body {
  font-family: "Sora", sans-serif;
  color: #687693;
  background-color: #fff;
  font-size: 15px;
  font-size: 1rem;
  overflow-x: hidden;
}

@media (max-width: 767px) {
  body {
    font-size: 14px;
  }
}

.hidden {
  display: none;
}

p {
  font-size: 15px;
  color: #687693;
  line-height: 1.8em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #1C1817;
  font-family: "Epilogue";
  font-weight: 600;
}

ul {
  padding-left: 0;
  margin: 0;
}

a {
  text-decoration: none;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

a:hover {
  text-decoration: none;
}

img {
  max-width: 100%;
}

.sr-only {
  display: none !important;
  opacity: 0;
  visibility: hidden;
}

/* 1.2 Global Elements */
.page-wrapper {
  position: relative;
  overflow: hidden;
}

.wow {
  visibility: hidden;
}

.fi:before {
  margin: 0;
}

.section-padding {
  padding: 120px 0;
}

@media (max-width: 991px) {
  .section-padding {
    padding: 90px 0;
  }
}

@media (max-width: 767px) {
  .section-padding {
    padding: 80px 0;
  }
}

/*** contact form error handling ***/
.contact-validation-active .error-handling-messages {
  width: 100% !important;
  margin-top: 15px !important;
}

.contact-validation-active label.error {
  color: red;
  font-size: 0.93333rem;
  font-weight: normal;
  margin: 5px 0 0 0;
  text-align: left;
  display: block;
}

.contact-validation-active #c-loader,
.contact-validation-active #loader {
  display: none;
  margin-top: 10px;
}

.contact-validation-active #c-loader i,
.contact-validation-active #loader i {
  font-size: 30px;
  font-size: 2rem;
  color: #E5F346;
  display: inline-block;
  -webkit-animation: rotating linear 2s infinite;
  animation: rotating linear 2s infinite;
}

.contact-validation-active #success,
.contact-validation-active #c-success,
.contact-validation-active #c-error,
.contact-validation-active #error {
  width: 100%;
  color: #fff;
  padding: 5px 10px;
  font-size: 16px;
  text-align: center;
  display: none;
}

@media (max-width: 767px) {
  .contact-validation-active #success,
  .contact-validation-active #c-success,
  .contact-validation-active #c-error,
  .contact-validation-active #error {
    font-size: 15px;
  }
}

.contact-validation-active #c-success,
.contact-validation-active #success {
  background-color: #009a00;
  border-left: 5px solid green;
  margin-bottom: 5px;
}

.contact-validation-active #c-error,
.contact-validation-active #error {
  background-color: #ff1a1a;
  border-left: 5px solid red;
}

@-webkit-keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.rotating {
  -webkit-animation: rotating 5s alternate infinite;
  animation: rotating 5s alternate infinite;
}

/*** back to top **/
.back-to-top {
  background-color: rgba(229, 243, 70, 0.7);
  width: 45px;
  height: 45px;
  line-height: 45px;
  border: 2px solid #E5F346;
  border-radius: 45px;
  text-align: center;
  display: none;
  position: fixed;
  z-index: 999;
  right: 15px;
  bottom: 15px;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

@media (max-width: 991px) {
  .back-to-top {
    width: 35px;
    height: 35px;
    line-height: 35px;
  }
}

.back-to-top:hover {
  background-color: #E5F346;
}

.back-to-top i {
  font-size: 18px;
  font-size: 1.2rem;
  color: #1C1817;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/** for popup image ***/
.mfp-wrap {
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99999;
}

.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
  opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}

/*** for fancybox video ***/
.fancybox-overlay {
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999 !important;
}

.fancybox-wrap {
  z-index: 99999 !important;
}

.wpo-section-title,
.wpo-section-title-s2 {
  margin-bottom: 60px;
  text-align: center;
}

@media (max-width: 767px) {
  .wpo-section-title,
  .wpo-section-title-s2 {
    margin-bottom: 40px;
  }
}

.wpo-section-title small,
.wpo-section-title-s2 small {
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  text-transform: uppercase;
  display: inline-block;
  padding: 5px 30px;
  background: #F5F6E4;
  margin-bottom: 30px;
}

@media (max-width: 991px) {
  .wpo-section-title small,
  .wpo-section-title-s2 small {
    margin-bottom: 15px;
  }
}

.wpo-section-title h2,
.wpo-section-title-s2 h2 {
  margin: 0;
  position: relative;
  font-family: "Epilogue";
  font-size: 58px;
  font-style: normal;
  font-weight: 500;
  line-height: 68px;
  text-transform: uppercase;
  color: #1C1817;
  margin-bottom: 20px;
}

@media (max-width: 1399px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2 {
    font-size: 50px;
  }
}

@media (max-width: 1199px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2 {
    font-size: 42px;
    line-height: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2 {
    font-size: 32px;
    line-height: 40px;
    margin-top: 5px;
  }
}

@media (max-width: 330px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2 {
    font-size: 30px;
  }
}

.wpo-section-title h2 span,
.wpo-section-title-s2 h2 span {
  position: relative;
  z-index: 1;
  display: inline-block;
}

.wpo-section-title h2 span::before,
.wpo-section-title-s2 h2 span::before {
  position: absolute;
  left: 5px;
  bottom: 19px;
  width: 96%;
  height: 19px;
  background: #E5F346;
  content: "";
  z-index: -1;
}

.wpo-section-title p,
.wpo-section-title-s2 p {
  font-size: 18px;
}

.wpo-section-title-s2 {
  text-align: left;
}

.theme-btn, .view-cart-btn {
  background: #E5F346;
  display: inline-block;
  font-weight: 600;
  padding: 20px 35px;
  padding-bottom: 16px;
  border: 0;
  text-transform: capitalize;
  position: relative;
  font-size: 15px;
  text-align: center;
  color: #1C1817;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  z-index: 1;
}

.theme-btn:hover, .view-cart-btn:hover, .theme-btn:focus, .view-cart-btn:focus, .theme-btn:active, .view-cart-btn:active {
  background: #dff01b;
  color: #1C1817;
}

@media (max-width: 991px) {
  .theme-btn, .view-cart-btn {
    font-size: 15px;
  }
}

@media (max-width: 767px) {
  .theme-btn, .view-cart-btn {
    padding: 12px 18px;
    font-size: 14px;
    padding-bottom: 8px;
  }
}

.theme-btn-s2 {
  color: #E5F346;
  display: inline-block;
  font-family: "Epilogue";
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  text-transform: uppercase;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .theme-btn-s2 {
    font-size: 14px;
  }
}

.theme-btn-s2:hover, .theme-btn-s2:focus, .theme-btn-s2:active {
  color: #adb927;
}

@media (max-width: 767px) {
  .theme-btn-s2 {
    font-size: 13px;
  }
}

.view-cart-btn {
  display: block;
  margin-top: 15px;
  border-radius: 5px;
  padding: 10px 45px;
  background: #E5F346;
  color: #1C1817;
}

.view-cart-btn::before {
  border: 1px dashed #adb927;
}

.view-cart-btn:after {
  display: none;
}

.view-cart-btn:hover {
  background-color: #e4f23c;
}

.view-cart-btn.s1 {
  background: #ddd;
}

.view-cart-btn.s1::before {
  border: 1px dashed #ddd;
}

.view-cart-btn.s1:hover {
  background-color: #c9c9c9;
}

.form input,
.form textarea,
.form select {
  border-color: #bfbfbf;
  border-radius: 0;
  outline: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #595959;
  font-style: normal;
}

.form input:focus,
.form textarea:focus,
.form select:focus {
  border-color: #E5F346;
  -webkit-box-shadow: 0 0 5px 0 #ecf676;
  -o-box-shadow: 0 0 5px 0 #ecf676;
  -ms-box-shadow: 0 0 5px 0 #ecf676;
  box-shadow: 0 0 5px 0 #ecf676;
}

.form ::-webkit-input-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form :-moz-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form ::-moz-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form :-ms-input-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form select {
  font-style: normal;
  background: url(../images/select-icon.png) no-repeat right center;
  display: inline-block;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  cursor: pointer;
}

.form select::-ms-expand {
  /* for IE 11 */
  display: none;
}

.form ::-webkit-input-placeholder {
  /* Edge */
  font-style: normal;
}

.form :-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-style: normal;
}

.form ::-ms-input-placeholder {
  font-style: normal;
}

.form ::placeholder {
  font-style: normal;
}

.social-links {
  overflow: hidden;
}

.social-links li {
  float: left;
  width: 35px;
  height: 35px;
  margin-right: 1px;
}

.social-links li a {
  background-color: #ecf676;
  width: 35px;
  height: 35px;
  line-height: 35px;
  display: block;
  color: #fff;
  text-align: center;
}

.social-links li a:hover {
  background-color: #E5F346;
}

.wpo-page-title {
  background: url(../images/page-title.jpg) no-repeat center top/cover;
  min-height: 380px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  z-index: 1;
}

@media (max-width: 767px) {
  .wpo-page-title {
    min-height: 250px;
  }
}

.wpo-page-title:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #1C1817;
  content: "";
  z-index: -1;
  opacity: .60;
}

.wpo-page-title .wpo-breadcumb-wrap {
  text-align: center;
  margin-top: 10px;
}

@media (max-width: 991px) {
  .wpo-page-title .wpo-breadcumb-wrap {
    margin-top: 0;
  }
}

.wpo-page-title .wpo-breadcumb-wrap h2 {
  font-size: 60px;
  color: #fff;
  line-height: 60px;
  margin-top: -10px;
  margin-bottom: 20px;
  font-weight: 400;
}

@media (max-width: 767px) {
  .wpo-page-title .wpo-breadcumb-wrap h2 {
    font-size: 30px;
    line-height: 35px;
    margin-bottom: 10px;
  }
}

.wpo-page-title .wpo-breadcumb-wrap ol {
  padding-left: 0;
  margin-bottom: 0;
}

.wpo-page-title .wpo-breadcumb-wrap ol li {
  display: inline-block;
  padding: 0px 5px;
  padding-right: 20px;
  color: #fff;
  position: relative;
  font-size: 20px;
  font-family: "Sora", sans-serif;
}

@media (max-width: 767px) {
  .wpo-page-title .wpo-breadcumb-wrap ol li {
    font-size: 18px;
  }
}

.wpo-page-title .wpo-breadcumb-wrap ol li:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 10px;
  height: 5px;
  background: #fff;
  border-radius: 30px;
}

.wpo-page-title .wpo-breadcumb-wrap ol li:last-child span {
  color: #cbd4fd;
}

.wpo-page-title .wpo-breadcumb-wrap ol li:last-child:after {
  display: none;
}

.wpo-page-title .wpo-breadcumb-wrap ol li a {
  color: #fff;
  font-size: 20px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-page-title .wpo-breadcumb-wrap ol li a:hover {
  color: #E5F346;
}

.preloader {
  background-color: #1C1817;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1000;
  overflow: hidden;
}

.preloader:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.preloader .vertical-centered-box {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}

.preloader .vertical-centered-box:after {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}

.preloader .vertical-centered-box .content {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  text-align: left;
  font-size: 0;
}

.preloader * {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.preloader .loader-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
          box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  margin-left: -60px;
  margin-top: -60px;
}

.preloader .loader-line-mask {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 120px;
  margin-left: -60px;
  margin-top: -60px;
  overflow: hidden;
  -webkit-transform-origin: 60px 60px;
  transform-origin: 60px 60px;
  -webkit-mask-image: -webkit-linear-gradient(top, #000000, rgba(0, 0, 0, 0));
  -webkit-animation: rotate 1.2s infinite linear;
  animation: rotate 1.2s infinite linear;
}

.preloader .loader-line-mask .loader-line {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
          box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.preloader #particles-background,
.preloader #particles-foreground {
  left: -51%;
  top: -51%;
  width: 202%;
  height: 202%;
  -webkit-transform: scale3d(0.5, 0.5, 1);
  transform: scale3d(0.5, 0.5, 1);
}

.preloader #particles-background {
  background: #2c2d44;
  background-image: linear-gradient(45deg, #3f3251 2%, #002025 100%);
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes fade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.25;
  }
}

@keyframes fade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.25;
  }
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.preloader [not-existing] {
  zoom: 1;
}

/*------------------------------------
	blog sidebar
------------------------------------*/
.blog-sidebar {
  /*** search-widget ***/
  /*** about-widget ***/
  /*** category-widget ***/
  /*** recent-post-widget ***/
  /*** instagram-widget ***/
  /*** wpo-newsletter-widget ***/
  /*** tag-widget ***/
  /*** wpo-contact-widget ***/
}

@media screen and (min-width: 992px) {
  .blog-sidebar {
    padding-left: 45px;
  }
}

@media (max-width: 991px) {
  .blog-sidebar {
    margin-top: 80px;
    max-width: 400px;
  }
}

@media (max-width: 767px) {
  .blog-sidebar {
    margin-top: 60px;
    max-width: 400px;
  }
}

.blog-sidebar .widget h3 {
  font-size: 23px;
  font-size: 1.53333rem;
  color: #1C1817;
  margin: 0 0 1.3em;
  position: relative;
  text-transform: capitalize;
  font-family: "Epilogue";
  padding-bottom: 20px;
  font-weight: 400;
}

.blog-sidebar .widget h3:before {
  content: "";
  background-color: #E5F346;
  width: 55px;
  height: 4px;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 10px;
}

.blog-sidebar .widget h3:after {
  content: "";
  background-color: #f2f2f2;
  width: 80%;
  height: 4px;
  position: absolute;
  left: 65px;
  bottom: 0;
  border-radius: 10px;
}

.blog-sidebar > .widget + .widget {
  margin-top: 65px;
}

.blog-sidebar .search-widget form div {
  position: relative;
}

.blog-sidebar .search-widget input {
  background-color: #fff;
  height: 70px;
  font-size: 16px;
  font-size: 1.06667rem;
  padding: 6px 50px 6px 20px;
  border: 0;
  border-radius: 5px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
          box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
}

.blog-sidebar .search-widget input:focus {
  background-color: rgba(229, 243, 70, 0.1);
}

.blog-sidebar .search-widget form button {
  background: transparent;
  font-size: 20px;
  font-size: 1.33333rem;
  color: #1C1817;
  background-color: #E5F346;
  border: 0;
  outline: 0;
  position: absolute;
  right: 10px;
  top: 52%;
  height: 50px;
  line-height: 50px;
  width: 50px;
  border-radius: 6px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.blog-sidebar .about-widget {
  text-align: center;
  background-color: #f9f9f9;
  padding: 45px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1200px) {
  .blog-sidebar .about-widget {
    padding: 35px;
  }
}

.blog-sidebar .about-widget .aw-shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.blog-sidebar .about-widget .img-holder {
  margin-bottom: 25px;
}

.blog-sidebar .about-widget .img-holder img {
  border-radius: 50%;
  width: 100%;
  max-width: 200px;
}

.blog-sidebar .about-widget ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.blog-sidebar .about-widget ul li {
  margin-right: 10px;
}

.blog-sidebar .about-widget ul li:last-child {
  margin-right: 0;
}

.blog-sidebar .about-widget ul li a {
  text-decoration: none;
  display: block;
  width: 40px;
  height: 40px;
  line-height: 44px;
  background-color: #fff;
  border-radius: 50%;
}

.blog-sidebar .about-widget h4 {
  font-size: 25px;
  font-weight: 400;
  margin-bottom: 15px;
}

.blog-sidebar .about-widget p {
  font-size: 15px;
  font-size: 1rem;
  margin-bottom: 1.2em;
  color: #525252;
}

.blog-sidebar .about-widget a {
  font-family: "Epilogue";
  font-size: 14px;
  font-size: 0.93333rem;
  color: #1C1817;
  text-decoration: underline;
}

.blog-sidebar .about-widget a:hover {
  color: #adb927;
}

.blog-sidebar .category-widget ul {
  list-style: none;
}

.blog-sidebar .category-widget ul li {
  font-size: 19px;
  font-size: 1.26667rem;
  position: relative;
}

@media (max-width: 767px) {
  .blog-sidebar .category-widget ul li {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.blog-sidebar .category-widget ul li span {
  position: absolute;
  right: 0;
}

.blog-sidebar .category-widget ul > li + li {
  margin-top: 12px;
  padding-top: 12px;
}

.blog-sidebar .category-widget ul a {
  display: block;
  color: #474f62;
  font-size: 16px;
  font-weight: 400;
}

.blog-sidebar .category-widget ul a span {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background-color: #F5F6E4;
  text-align: center;
  border-radius: 50%;
  font-size: 18px;
}

@media (max-width: 767px) {
  .blog-sidebar .category-widget ul a span {
    font-size: 14px;
    font-size: 0.93333rem;
  }
}

.blog-sidebar .category-widget ul a:hover,
.blog-sidebar .category-widget ul li:hover:before {
  color: #adb927;
}

.blog-sidebar .recent-post-widget .post {
  overflow: hidden;
}

.blog-sidebar .recent-post-widget .posts > .post + .post {
  margin-top: 15px;
  padding-top: 15px;
}

.blog-sidebar .recent-post-widget .post .img-holder {
  width: 90px;
  float: left;
}

.blog-sidebar .recent-post-widget .post .img-holder img {
  border-radius: 6px;
}

.blog-sidebar .recent-post-widget .post .details {
  width: calc(100% - 90px);
  float: left;
  padding-left: 20px;
}

.blog-sidebar .recent-post-widget .post h4 {
  font-size: 17px;
  font-size: 1.13333rem;
  font-weight: 500;
  line-height: 1.3em;
  margin: 0 0 0.3em;
  font-family: "Sora", sans-serif;
}

@media (max-width: 1199px) {
  .blog-sidebar .recent-post-widget .post h4 {
    margin: 0;
  }
}

@media (max-width: 767px) {
  .blog-sidebar .recent-post-widget .post h4 {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.blog-sidebar .recent-post-widget .post h4 a {
  display: inline-block;
  color: #1C1817;
}

.blog-sidebar .recent-post-widget .post h4 a:hover {
  color: #adb927;
}

.blog-sidebar .recent-post-widget .post .details .date {
  font-size: 13px;
  font-size: 0.86667rem;
  color: #424740;
}

.blog-sidebar .recent-post-widget .post .details .date i {
  display: inline-block;
  padding-right: 7px;
}

.blog-sidebar .wpo-instagram-widget ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  list-style: none;
}

.blog-sidebar .wpo-instagram-widget ul li {
  -ms-flex: 0 0 33.33%;
  -webkit-box-flex: 0;
  flex: 0 0 33.33%;
  max-width: 33.33%;
  margin-bottom: 5px;
  padding: 0px 3px;
}

.blog-sidebar .wpo-instagram-widget ul li img {
  width: 100%;
}

.blog-sidebar .wpo-newsletter-widget p {
  font-size: 18px;
  color: #1C1817;
  text-align: left;
}

.blog-sidebar .wpo-newsletter-widget span {
  font-size: 15px;
  color: #424740;
}

.blog-sidebar .wpo-newsletter-widget span a {
  color: #1C1817;
  font-weight: 400;
}

.blog-sidebar .wpo-newsletter-widget span a:hover {
  color: #E5F346;
}

.blog-sidebar .wpo-newsletter-widget .form input {
  border: 0;
  display: block;
  width: 100%;
  height: 55px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
          box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
  padding: 15px;
  text-align: center;
}

.blog-sidebar .wpo-newsletter-widget .form input:focus {
  -webkit-box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
          box-shadow: 0px 1px 10px rgba(16, 45, 72, 0.15);
}

.blog-sidebar .wpo-newsletter-widget .form button {
  background: #E5F346;
  width: 100%;
  height: 55px;
  border-radius: 10px;
  text-align: center;
  color: #1C1817;
  border: none;
  margin: 20px 0;
}

.blog-sidebar .tag-widget ul {
  overflow: hidden;
  list-style: none;
}

.blog-sidebar .tag-widget ul li {
  float: left;
  margin: 0 8px 8px 0;
}

.blog-sidebar .tag-widget ul li a {
  font-size: 15px;
  font-size: 1rem;
  display: inline-block;
  padding: 5px 18px;
  color: #1C1817;
  background: #F5F6E4;
  border-radius: 5px;
}

.blog-sidebar .tag-widget ul li a:hover {
  background: #E5F346;
  color: #1C1817;
}

.blog-sidebar .wpo-contact-widget {
  border: 0;
  background: #1C1817;
  position: relative;
  background-size: cover;
  z-index: 1;
  padding: 30px 40px;
}

.blog-sidebar .wpo-contact-widget:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
}

@media (max-width: 1200px) {
  .blog-sidebar .wpo-contact-widget {
    padding: 20px;
  }
}

.blog-sidebar .wpo-contact-widget h2 {
  font-size: 36px;
  font-weight: 400;
  text-align: left;
  color: #fff;
  margin-bottom: 20px;
}

.blog-sidebar .wpo-contact-widget h2::before {
  left: 28px;
  background: url(../images/Single-title-shape2.html) no-repeat right center;
}

.blog-sidebar .wpo-contact-widget p {
  color: #fff;
  font-size: 16px;
}

.blog-sidebar .wpo-contact-widget a {
  display: inline-block;
  padding: 10px 20px;
  border: 1px solid #fff;
  font-size: 18px;
  color: #fff;
  padding-right: 90px;
  position: relative;
  margin-top: 10px;
}

.blog-sidebar .wpo-contact-widget a::before {
  font-family: "themify";
  content: "\e628";
  font-size: 18px;
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media screen and (min-width: 992px) {
  .wpo-blog-pg-section.blog-pg-left-sidebar .blog-sidebar {
    padding-left: 0px;
    padding-right: 45px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-blog-single-section.wpo-blog-single-left-sidebar-section .blog-sidebar {
    padding-left: 0px;
    padding-right: 45px;
  }
}

/**** pagination ****/
.pagination-wrapper {
  text-align: center;
  margin-top: 60px;
}

@media (max-width: 991px) {
  .pagination-wrapper {
    text-align: left;
    margin-top: 40px;
  }
}

.pagination-wrapper .pg-pagination {
  display: inline-block;
  overflow: hidden;
  list-style-type: none;
  text-align: center;
}

.pagination-wrapper .pg-pagination li {
  float: left;
  margin-right: 10px;
}

@media (max-width: 767px) {
  .pagination-wrapper .pg-pagination li {
    margin-right: 5px;
  }
}

.pagination-wrapper .pg-pagination li:last-child {
  margin-right: 0;
}

.pagination-wrapper .pg-pagination li a {
  background-color: transparent;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-size: 1.06667rem;
  font-weight: 600;
  color: #1C1817;
  background: #F5F6E4;
  display: block;
}

@media (max-width: 991px) {
  .pagination-wrapper .pg-pagination li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 15px;
    font-size: 1rem;
  }
}

.pagination-wrapper .pg-pagination .active a,
.pagination-wrapper .pg-pagination li a:hover {
  background: #E5F346;
  border-color: #E5F346;
  color: #1C1817;
}

.pagination-wrapper .pg-pagination .fi:before {
  font-size: 15px;
  font-size: 1rem;
}

.pagination-wrapper-left {
  text-align: left;
}

.pagination-wrapper-right {
  text-align: right;
}

@media screen and (min-width: 1200px) {
  .pagination-wrapper-right {
    padding-right: 50px;
  }
}

@media (max-width: 991px) {
  .pagination-wrapper-right {
    margin-top: 45px;
    text-align: left;
  }
}

/*--------------------------------------------------------------
2. header
--------------------------------------------------------------*/
/*-- 2.1 topbar --*/
.topbar {
  background: #adb927;
  padding: 0px 50px;
}

@media (max-width: 1799px) {
  .topbar {
    padding: 0 10px;
  }
}

@media (max-width: 1699px) {
  .topbar {
    padding: 0;
  }
}

@media (max-width: 991px) {
  .topbar {
    text-align: center;
    padding: 0;
  }
}

@media (max-width: 767px) {
  .header-style-1 {
    text-align: center;
    padding: 20px 0;
  }
}

.topbar {
  font-size: 16px;
  color: #fff;
  margin: 0;
}

.topbar ul {
  overflow: hidden;
  list-style: none;
  float: left;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .topbar ul {
    float: none;
    display: inline-block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.topbar ul li {
  font-size: 16px;
  float: left;
  padding: 15px 10px;
  color: #fff;
  line-height: 1em;
}

@media (max-width: 991px) {
  .topbar ul li {
    padding: 15px 10px;
  }
}

.topbar .contact-info {
  float: right;
}

@media (max-width: 767px) {
  .topbar .contact-info {
    float: none;
  }
}

.topbar .contact-info ul li a {
  color: #e4e4e4;
}

.topbar .contact-info ul li a:hover {
  color: #E5F346;
}

.topbar ul li:first-child {
  padding-left: 0;
}

.topbar .contact-intro ul {
  float: left;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .topbar .contact-intro ul {
    float: none;
  }
}

@media (max-width: 1200px) {
  .topbar ul li {
    font-size: 12px;
    padding: 15px 10px;
  }
}

@media (max-width: 991px) {
  .topbar ul li {
    font-size: 13px;
  }
  .topbar {
    padding: 10px 0;
  }
}

@media (max-width: 767px) {
  .topbar ul li {
    float: left;
    border: none;
    padding: 8px 6px;
    font-size: 13px;
  }
}

@media (max-width: 590px) {
  .topbar ul li {
    float: none;
  }
  .topbar .contact-info ul li {
    float: left;
  }
  .topbar ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.topbar .theme-btn-s2 {
  padding: 15px 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: -3px;
  color: #fff !important;
}

.topbar ul .fi {
  position: relative;
  top: 1px;
  display: inline-block;
  padding-right: 5px;
}

.topbar ul .fi:before {
  font-size: 15px;
  font-size: 1rem;
  color: #fff;
}

/* 2.2 navigation */
.wpo-site-header {
  /* navigation open and close btn hide for width screen */
  /* style for navigation less than 992px */
  /*navbar collaps less then 992px*/
  /*** cart-search-contact ***/
  background-color: transparent;
  position: absolute;
  left: 0;
  top: 28px;
  width: 100%;
  z-index: 11;
}

@media (max-width: 767px) {
  .wpo-site-header {
    position: unset;
  }
}

.wpo-site-header .navigation {
  background-color: #fff;
  margin-bottom: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

.sticky-header {
  width: 100%;
  position: fixed;
  left: 0;
  top: -400px;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 1s;
  transition: all 1s;
  -webkit-box-shadow: 0 -5px 15px #086ad84a;
  box-shadow: 0 -5px 15px #086ad84a;
}

.sticky-on {
  opacity: 1;
  top: 0;
  visibility: visible;
}

.wpo-site-header .navigation > .container {
  position: relative;
}

@media (min-width: 991px) {
  .wpo-site-header {
    padding: 0 100px;
  }
}

.wpo-site-header .row {
  width: 100%;
}

.wpo-site-header #navbar {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.wpo-site-header #navbar > ul li a:hover,
.wpo-site-header #navbar > ul li a:focus {
  text-decoration: none;
  color: #adb927;
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar {
    /*** hover effect ***/
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-right: -40px;
  }
  .wpo-site-header #navbar li {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 15px;
    top: 58%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  .wpo-site-header #navbar > ul > li:hover > .sub-menu {
    top: 100%;
    visibility: visible;
    opacity: 1;
  }
  .wpo-site-header #navbar .sub-menu > li:hover > .sub-menu {
    left: 100%;
    visibility: visible;
    opacity: 1;
  }
}

.wpo-site-header .container-fluid {
  padding-right: 0;
}

@media (max-width: 991px) {
  .wpo-site-header {
    /* class for show hide navigation */
  }
  .wpo-site-header .container {
    width: 100%;
    max-width: 100%;
  }
  .wpo-site-header .header-right {
    right: -10px;
  }
  .wpo-site-header .navbar-header button span {
    background-color: #fff;
    width: 20px;
    display: block;
    height: 2px;
    margin-bottom: 5px;
  }
  .wpo-site-header .navbar-header button span:last-child {
    margin: 0;
  }
}

.wpo-site-header #navbar ul {
  list-style: none;
}

@media (max-width: 991px) and (max-width: 991px) {
  .wpo-site-header #navbar {
    top: 0;
    visibility: visible;
    opacity: 1;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul {
    z-index: 101;
  }
  .wpo-site-header #navbar ul a {
    color: #000;
  }
  .wpo-site-header #navbar ul a:hover,
  .wpo-site-header #navbar ul li.current a {
    color: #adb927;
  }
  .wpo-site-header #navbar .close-navbar .ti-close {
    position: relative;
    top: 1px;
  }
  .wpo-site-header .navbar-toggler .first-angle,
  .wpo-site-header .navbar-toggler .last-angle {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header .x-close .middle-angle {
    opacity: 0;
  }
  .wpo-site-header .x-close .first-angle {
    position: absolute;
    -webkit-transform: rotate(-44deg);
    top: 16px;
    left: 10px;
  }
  .wpo-site-header .x-close .last-angle {
    -webkit-transform: rotate(44deg);
    position: absolute;
    top: 16px;
    right: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-site-header .navbar-header .navbar-brand {
    font-size: 24px;
  }
  .wpo-site-header #navbar .navbar-nav {
    margin: 0;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .navbar-collapse.collapse {
    display: none;
  }
  .wpo-site-header .navbar-collapse.collapse.in {
    display: block;
  }
  .wpo-site-header .navbar-header .collapse,
  .wpo-site-header .navbar-toggle {
    display: block;
  }
  .wpo-site-header .navbar-header {
    float: none;
    position: relative;
    z-index: 99;
    text-align: center;
  }
  .wpo-site-header .navbar-right {
    float: none;
  }
  .wpo-site-header .navbar-nav {
    float: none;
  }
  .wpo-site-header .navbar-nav > li {
    float: none;
  }
}

.wpo-site-header .header-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  z-index: 991;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  right: -10px;
}

@media (max-width: 349px) {
  .wpo-site-header .header-right {
    right: -20px;
  }
}

.wpo-site-header .header-right button {
  padding: 0;
  border: 0;
  outline: 0;
}

.wpo-site-header .header-right > div {
  float: left;
  position: relative;
  margin-right: 10px;
}

@media (max-width: 450px) {
  .wpo-site-header .header-right > div {
    margin-right: 0px;
  }
}

.wpo-site-header .header-right > div:last-child {
  margin: 0 0 0 10px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-site-header .header-right > div:last-child {
    margin: 3px 0 0 10px;
  }
}

.wpo-site-header .header-right .header-search-form-wrapper {
  position: relative;
}

.wpo-site-header .header-right .header-search-form-wrapper .fi:before {
  font-size: 18px;
}

.wpo-site-header .header-right .search-toggle-btn,
.wpo-site-header .header-right .cart-toggle-btn {
  background-color: transparent;
  line-height: 0.66em;
  color: #232f4b;
  width: 50px;
  height: 50px;
  border: 0;
  border-radius: 50%;
}

@media (max-width: 1200px) {
  .wpo-site-header .header-right .search-toggle-btn,
  .wpo-site-header .header-right .cart-toggle-btn {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .search-toggle-btn,
  .wpo-site-header .header-right .cart-toggle-btn {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .search-toggle-btn .fi:before,
  .wpo-site-header .header-right .cart-toggle-btn .fi:before {
    font-size: 16px;
  }
}

.wpo-site-header .header-right .search-toggle-btn .ti-close,
.wpo-site-header .header-right .cart-toggle-btn .ti-close {
  display: block;
  line-height: 20px;
  position: relative;
  top: -2px;
}

/*** cart-search-contact ***/
.cart-search-contact button {
  background: transparent;
  padding: 0;
  border: 0;
  outline: 0;
}

.cart-search-contact .fi {
  font-size: 30px;
  font-size: 2rem;
  color: #1C1817;
}

.cart-search-contact > div {
  float: left;
  position: relative;
}

.cart-search-contact > div + div {
  margin-left: 12px;
}

.cart-search-contact .header-search-form {
  position: absolute;
  width: 350px;
  right: 0;
  top: 75px;
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  padding: 30px;
  border-radius: 6px;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-box-shadow: -2px 18px 40px -9px #999999;
          box-shadow: -2px 18px 40px -9px #999999;
}

@media (max-width: 991px) {
  .cart-search-contact .header-search-form {
    top: 65px;
  }
}

@media (max-width: 767px) {
  .cart-search-contact .header-search-form {
    right: 15px;
  }
}

@media (max-width: 575px) {
  .cart-search-contact .header-search-form {
    width: 260px;
    padding: 15px;
  }
}

.cart-search-contact .header-search-form-wrapper .fi:before {
  font-size: 18px;
  font-size: 1.2rem;
}

.cart-search-contact form div {
  position: relative;
}

.cart-search-contact form div button {
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.cart-search-contact form div button .fi:before {
  font-size: 18px !important;
}

.cart-search-contact input {
  width: 100%;
  height: 50px;
  padding: 6px 20px;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #DADADA;
}

.cart-search-contact input:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #E5F346;
}

.wpo-site-header .header-right .header-search-content-toggle {
  opacity: 1;
  visibility: visible;
  right: 0;
  -webkit-transform: scale(1);
          transform: scale(1);
}

.mini-cart .cart-count {
  background: #adb927;
  width: 19px;
  height: 19px;
  font-size: 10px;
  line-height: 17px;
  color: white;
  position: absolute;
  top: 6px;
  right: 0px;
  border-radius: 50%;
  font-weight: 600;
}

.mini-cart-content {
  background: #fff;
  width: 320px;
  height: 100%;
  z-index: 100;
  position: fixed;
  right: -320px;
  top: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-box-shadow: -2px 18px 40px -9px #999999;
          box-shadow: -2px 18px 40px -9px #999999;
}

@media (max-width: 575px) {
  .mini-cart-content {
    width: 290px;
  }
}

.mini-cart-content .mini-cart-close {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #333333;
  color: white;
  font-size: 0px;
  text-align: center;
  overflow: hidden;
  position: relative;
  float: right;
  line-height: 54px;
  border-radius: 3px;
  right: 10px;
  top: 20px;
}

.mini-cart-content .mini-cart-close i {
  font-size: 15px;
}

.mini-cart-content p {
  font-size: 15px;
  font-size: 1rem;
  font-weight: 500;
  color: #1C1817;
  margin: 0;
  text-transform: uppercase;
}

.mini-cart-content .mini-cart-items {
  padding: 100px 20px 25px;
}

.mini-cart-content .mini-cart-item {
  padding-top: 15px;
  margin-top: 15px;
  border-top: 1px solid #efefef;
}

.mini-cart-content .mini-cart-item:first-child {
  padding-top: 0;
  margin-top: 0;
  border-top: 0;
}

.mini-cart-content .mini-cart-item-image {
  width: 50px;
  height: 50px;
  border: 2px solid #f3f3f3;
  float: left;
  margin-right: 15px;
}

.mini-cart-content .mini-cart-item-image a,
.mini-cart-content .mini-cart-item-image img {
  display: block;
  width: 46px;
  height: 46px;
}

.mini-cart-content .mini-cart-item-des {
  position: relative;
  overflow: hidden;
}

.mini-cart-content .mini-cart-item-des a {
  font-size: 14px;
  font-size: 0.93333rem;
  font-weight: 600;
  text-align: left;
  color: #1C1817;
}

.mini-cart-content .mini-cart-item-des a:hover {
  color: #adb927;
}

.mini-cart-content .mini-cart-item-des .mini-cart-item-price {
  font-size: 13px;
  font-family: "Epilogue";
  color: #888;
  display: block;
  margin-top: 3px;
}

.mini-cart-content .mini-cart-item-des .mini-cart-item-quantity {
  font-size: 12px;
  color: #444;
  display: block;
  position: absolute;
  right: 0;
  top: 6px;
}

.mini-cart-content .visible-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  opacity: .02;
  z-index: -1;
}

.mini-cart-content .mini-cart-action {
  padding: 20px 0 30px;
  text-align: center;
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.mini-cart-content .mini-cart-action .theme-btn-s2 {
  float: right;
}

.mini-cart-content .mini-cart-action .mini-btn {
  width: 240px;
}

.mini-cart-content .mini-cart-action .mini-checkout-price {
  font-size: 16px;
  font-family: "Epilogue";
  font-weight: 600;
  color: #1C1817;
  display: block;
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.mini-cart-content .mini-cart-action .mini-checkout-price span {
  color: #adb927;
  font-size: 20px;
  font-family: "Epilogue";
}

.mini-cart-content-toggle,
.mini-wislist-content-toggle {
  opacity: 1;
  visibility: visible;
  right: 0;
}

@media (max-width: 991px) {
  .page-wrapper {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .body-overlay:before {
    content: "";
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
}

.header-search-area {
  background: #fff;
  width: 100%;
  padding: 100px 0;
  text-align: center;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
}

.header-search-area h3 {
  font-size: 45px;
  margin: 0 0 1em;
}

.header-search-area form {
  position: relative;
}

.header-search-area input {
  height: 50px;
  font-size: 18px;
  font-size: 1.2rem;
  border-radius: 0;
  border: 0;
  border-bottom: 1px solid #d0d0d0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.header-search-area input:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-color: #E5F346;
}

.header-search-area form button {
  background: transparent;
  border: 0;
  font-size: 20px;
  font-size: 1.33333rem;
  position: absolute;
  right: 0;
  bottom: 10px;
}

.header-search-area form button:focus {
  border: none;
  outline: none;
}

.header-search-area .close-form {
  position: absolute;
  right: 35px;
  top: 35px;
  bottom: auto;
  padding-bottom: 5px;
  border-bottom: 1px solid;
}

.header-search-area .close-form button {
  background: transparent;
  border: 0;
  outline: 0;
  font-size: 13px;
  font-size: 0.86667rem;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 2px;
  z-index: 99;
}

.header-search-area .close-form button:focus {
  border: 0;
  outline: 0;
}

.wpo-site-header {
  /* navigation open and close btn hide for width screen */
  /* style for navigation less than 992px */
  /*navbar collaps less then 992px*/
}

.wpo-site-header .navigation {
  background-color: #fff;
  margin-bottom: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

.wpo-site-header .navigation > .container {
  position: relative;
}

@media (max-width: 991px) {
  .wpo-site-header .navigation {
    padding: 15px 0;
  }
}

@media (max-width: 767px) {
  .wpo-site-header .navigation {
    padding: 10px 0;
  }
}

.wpo-site-header #navbar {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.wpo-site-header #navbar > ul li a.active,
.wpo-site-header #navbar > ul li a:hover,
.wpo-site-header #navbar > ul li a:focus {
  text-decoration: none;
  color: #adb927;
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar {
    /*** hover effect ***/
  }
  .wpo-site-header #navbar li {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > a {
    padding: 35px 20px;
    display: block;
    color: #424740;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    font-family: "Epilogue";
    text-transform: uppercase;
  }
}

@media screen and (min-width: 992px) and (max-width: 1870px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 35px 18px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar > ul .sub-menu {
    background-color: #fff;
    -webkit-box-shadow: 0px 2px 20px 0px rgba(62, 65, 159, 0.09);
            box-shadow: 0px 2px 20px 0px rgba(62, 65, 159, 0.09);
    width: 220px;
    position: absolute;
    padding: 20px 0;
    left: 0;
    top: 115%;
    z-index: 10;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .wpo-site-header #navbar > ul .sub-menu {
    width: 230px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar > ul > li .sub-menu a {
    font-size: 14px;
    display: block;
    padding: 10px 15px;
    color: #424740;
    position: relative;
    overflow: hidden;
    font-weight: 400;
    text-transform: uppercase;
    font-family: "Epilogue";
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .wpo-site-header #navbar > ul > li .sub-menu a {
    font-size: 15px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar > ul > li .sub-menu a:after {
    position: absolute;
    left: 15px;
    bottom: 4px;
    width: 0px;
    height: 2px;
    content: "";
    background: #E5F346;
    -webkit-transition: all .3s;
    transition: all .3s;
    opacity: 0;
    visibility: hidden;
  }
  .wpo-site-header #navbar > ul > li .sub-menu a:hover:after, .wpo-site-header #navbar > ul > li .sub-menu a.active:after {
    width: 40px;
    opacity: 1;
    visibility: visible;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu .sub-menu {
    left: 115%;
    top: 0;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  .wpo-site-header #navbar > ul > li:hover > .sub-menu {
    top: 100%;
    visibility: visible;
    opacity: 1;
  }
  .wpo-site-header #navbar .sub-menu > li:hover > .sub-menu {
    left: 100%;
    visibility: visible;
    opacity: 1;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li a {
    display: block;
    font-size: 15px;
    color: #fff;
    font-weight: 600;
  }
  .wpo-site-header #navbar > ul > li a:hover, .wpo-site-header #navbar > ul > li a.active {
    color: #fff;
  }
  .wpo-site-header #navbar > ul .sub-menu > li:last-child {
    border-bottom: 0;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu a {
    padding: 8px 15px 8px 45px;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu .sub-menu a {
    padding: 8px 15px 8px 65px;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a {
    position: relative;
    color: #ddd;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a:hover, .wpo-site-header #navbar > ul .menu-item-has-children > a.active {
    color: #fff;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a.rotate:before {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
    top: 29%;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a.rotate {
    color: #fff;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header .navbar-header .open-btn {
    display: none;
  }
  .wpo-site-header #navbar .close-navbar {
    display: none;
  }
}

.wpo-site-header .menu-close {
  display: none;
}

@media (max-width: 991px) {
  .wpo-site-header {
    /* class for show hide navigation */
  }
  .wpo-site-header .container {
    width: 100%;
  }
  .wpo-site-header .mobail-menu button {
    background-color: #E5F346;
    width: 40px;
    height: 35px;
    border: 0;
    padding: 5px 10px;
    outline: 0;
    position: relative;
    z-index: 20;
  }
  .wpo-site-header .mobail-menu button:focus {
    outline: none;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .wpo-site-header .mobail-menu button span {
    background-color: #1C1817;
    width: 20px;
    display: block;
    height: 2px;
    margin-bottom: 5px;
  }
  .wpo-site-header .mobail-menu button span:last-child {
    margin: 0;
  }
  .wpo-site-header #navbar {
    background: #1C1817;
    display: block !important;
    width: 300px;
    height: 100%;
    margin: 0;
    padding: 0;
    border: 0;
    position: fixed;
    left: -320px;
    top: 0px;
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
  }
  .wpo-site-header #navbar:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: url(../images/noice.png);
    z-index: -1;
  }
  .wpo-site-header #navbar .navbar-nav {
    display: block;
  }
  .wpo-site-header #navbar > ul {
    position: relative;
    z-index: 101;
  }
  .wpo-site-header #navbar ul a {
    color: #000;
  }
  .wpo-site-header #navbar ul a:hover,
  .wpo-site-header #navbar ul li.current a {
    color: #adb927;
  }
  .wpo-site-header #navbar .navbar-nav {
    height: 100%;
    overflow: auto;
    padding-bottom: 60px;
  }
  .wpo-site-header #navbar .close-navbar {
    background-color: #fff;
    width: 40px;
    height: 40px;
    color: #000;
    border: 0;
    outline: none;
    position: absolute;
    left: -41px;
    top: 90px;
    z-index: 20;
  }
  .wpo-site-header #navbar .close-navbar .ti-close {
    position: relative;
    top: 1px;
  }
  .wpo-site-header .menu-close {
    display: block;
    z-index: 99;
    background: none;
    text-align: center;
    color: #fff;
    border: 0;
    text-align: center;
    margin-left: auto;
    margin-top: 30px;
    margin-right: 30px;
    margin-bottom: 30px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #fff;
  }
  .wpo-site-header .slideInn {
    left: 0 !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
  .wpo-site-header .navbar-toggler .first-angle,
  .wpo-site-header .navbar-toggler .last-angle {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header .x-close .middle-angle {
    opacity: 0;
  }
  .wpo-site-header .x-close .first-angle {
    position: absolute;
    -webkit-transform: rotate(-44deg);
    top: 16px;
    left: 10px;
  }
  .wpo-site-header .x-close .last-angle {
    -webkit-transform: rotate(44deg);
    position: absolute;
    top: 16px;
    right: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-site-header .navbar-header .navbar-brand {
    font-size: 24px;
  }
  .wpo-site-header #navbar .navbar-nav {
    margin: 0;
  }
}

.wpo-site-header .navbar-header .navbar-brand img {
  max-width: 170px;
}

@media (max-width: 991px) {
  .wpo-site-header .navbar-collapse.collapse {
    display: none;
  }
  .wpo-site-header .navbar-collapse.collapse.in {
    display: block;
  }
  .wpo-site-header .navbar-header .collapse,
  .wpo-site-header .navbar-toggle {
    display: block;
  }
  .wpo-site-header .navbar-header {
    float: none;
  }
  .wpo-site-header .navbar-right {
    float: none;
  }
  .wpo-site-header .navbar-nav {
    float: none;
  }
  .wpo-site-header .navbar-nav > li {
    float: none;
  }
}

@media (max-width: 440px) {
  .wpo-site-header .navbar-header .navbar-brand img {
    max-width: 170px;
  }
}

@media (max-width: 370px) {
  .wpo-site-header .navbar-header .navbar-brand img {
    max-width: 140px;
  }
}

@media (max-width: 1700px) {
  .wpo-site-header {
    padding: 0 30px;
  }
}

@media (max-width: 1500px) {
  .wpo-site-header {
    padding: 0 10px;
  }
}

@media (max-width: 767px) {
  .wpo-site-header {
    padding: 0;
  }
}

@media (max-width: 1400px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 30px 10px;
  }
}

@media (max-width: 1200px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 30px 15px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li > a {
    font-size: 15px;
  }
}

@media (max-width: 767px) {
  .header-right .theme-btn, .header-right .view-cart-btn {
    padding: 12px 12px;
  }
  .wpo-site-header .header-right .close-form {
    display: none;
  }
}

.wpo-header-style-1 {
  top: 0;
  padding-left: 290px;
}

@media (max-width: 1199px) {
  .wpo-header-style-1 {
    padding-left: 0;
    background: #fff;
  }
}

.wpo-header-style-1 .navigation {
  background: none;
}

.wpo-header-style-1 .navigation .row {
  background: #fff;
  padding: 0 20px;
}

@media (max-width: 991px) {
  .wpo-header-style-1 .navigation .row {
    padding: 0px;
  }
}

.wpo-header-style-1 .navigation .row:before {
  position: absolute;
  left: -70px;
  top: 0;
  width: 78px;
  background: #fff;
  content: "";
  height: 100%;
}

@media (max-width: 1199px) {
  .wpo-header-style-1 .navigation .row:before {
    display: none;
  }
}

@media (max-width: 1600px) {
  .wpo-header-style-1 .header-right .theme-btn, .wpo-header-style-1 .header-right .view-cart-btn {
    padding: 20px 25px;
    font-size: 14px;
  }
}

@media (max-width: 1399px) {
  .wpo-header-style-1 .header-right .theme-btn, .wpo-header-style-1 .header-right .view-cart-btn {
    padding: 10px;
    font-size: 13px;
  }
}

@media (max-width: 767px) {
  .wpo-header-style-1 .header-right .theme-btn, .wpo-header-style-1 .header-right .view-cart-btn {
    padding: 10px 5px;
    font-size: 10px;
    position: relative;
    right: 10px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 15px 30px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header,
  .wpo-header-style-2 {
    z-index: 99999;
  }
}

.wpo-site-header .navigation.sticky-header {
  padding: 0 100px;
  background: #fff;
  z-index: 111;
}

@media (max-width: 1700px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 0 50px;
  }
}

@media (max-width: 1400px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 0 20px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 20px;
  }
}

@media (max-width: 575px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 20px 10px;
  }
}

/* wpo-header-style-2 */
.wpo-header-style-2 {
  position: relative;
  top: 0;
}

/* wpo-header-style-4 */
.wpo-header-style-4 {
  padding: 0px 50px;
  top: 0;
  z-index: 111;
}

@media (max-width: 1799px) {
  .wpo-header-style-4 {
    padding: 0px 10px;
  }
}

@media (max-width: 1699px) {
  .wpo-header-style-4 {
    padding: 0px;
  }
}

.wpo-header-style-4 .row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 370px) {
  .wpo-header-style-4 .navbar-header .navbar-brand img {
    max-width: 120px;
  }
}

.wpo-header-style-4 .navigation {
  background: transparent;
  z-index: 11;
}

@media (max-width: 991px) {
  .wpo-header-style-4 .navigation {
    padding: 0;
  }
}

@media (max-width: 767px) {
  .wpo-header-style-4 .navigation {
    background: #1C1817;
  }
}

.wpo-header-style-4 .navigation.sticky-header {
  background: #1C1817;
  padding: 0;
  z-index: 111;
}

.wpo-header-style-4 .navigation.sticky-header .row {
  border: 0;
}

@media (max-width: 991px) {
  .wpo-header-style-4 .container {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-header-style-4 .header-right {
  right: -10px;
}

@media (max-width: 767px) {
  .wpo-header-style-4 .header-right .btns {
    margin-top: 0 !important;
  }
}

.wpo-header-style-4 #navbar > ul > li > a {
  color: #fff;
}

@media (max-width: 1499px) {
  .wpo-header-style-4 #navbar > ul > li > a {
    padding: 30px 15px;
  }
}

@media (max-width: 1399px) {
  .wpo-header-style-4 #navbar > ul > li > a {
    padding: 25px 10px;
  }
}

@media (max-width: 991px) {
  .wpo-header-style-4 #navbar > ul > li > a {
    padding: 15px 30px;
  }
}

.wpo-header-style-4 .price-btn {
  padding: 15px 20px;
  background: #E5F346;
  display: inline-block;
  width: 290px;
  position: relative;
}

@media (max-width: 1399px) {
  .wpo-header-style-4 .price-btn {
    width: 250px;
  }
}

@media (max-width: 1199px) {
  .wpo-header-style-4 .price-btn {
    width: 60px;
  }
}

.wpo-header-style-4 .price-btn:hover {
  background: #dff01b;
}

.wpo-header-style-4 .price-btn span {
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  color: #1C1817;
  font-family: "Epilogue";
}

@media (max-width: 1199px) {
  .wpo-header-style-4 .price-btn span {
    display: none;
  }
}

.wpo-header-style-4 .price-btn p {
  margin-bottom: 0;
  font-size: 29px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: uppercase;
  font-family: "Epilogue";
  color: #1C1817;
  margin-top: 6px;
}

@media (max-width: 1199px) {
  .wpo-header-style-4 .price-btn p {
    display: none;
  }
}

.wpo-header-style-4 .price-btn i {
  position: absolute;
  right: 25px;
  top: 25px;
  width: 45px;
  height: 45px;
  background: #1C1817;
  border-radius: 50%;
  text-align: center;
  line-height: 45px;
  color: #fff;
}

@media (max-width: 1199px) {
  .wpo-header-style-4 .price-btn i {
    position: relative;
    width: 45px;
    height: 45px;
    display: block;
    top: 0;
    right: 12px;
  }
}

.wpo-header-style-4 .price-btn i:before {
  position: relative;
  left: -2px;
}

/*--------------------------------------------------------------
3. content
--------------------------------------------------------------*/
/*3.1 wpo-hero-slider*/
.wpo-hero-slider,
.wpo-hero-slider-s2 {
  width: 100%;
  height: 960px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  z-index: 0;
}

@media (max-width: 991px) {
  .wpo-hero-slider,
  .wpo-hero-slider-s2 {
    height: 600px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider,
  .wpo-hero-slider-s2 {
    height: 500px;
  }
}

.wpo-hero-slider .swiper-slide,
.wpo-hero-slider-s2 .swiper-slide {
  overflow: hidden;
}

.wpo-hero-slider .swiper-container,
.wpo-hero-slider .hero-container,
.wpo-hero-slider-s2 .swiper-container,
.wpo-hero-slider-s2 .hero-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.wpo-hero-slider .slide-inner,
.wpo-hero-slider .hero-inner,
.wpo-hero-slider-s2 .slide-inner,
.wpo-hero-slider-s2 .hero-inner {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  background-size: cover;
  background-position: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: left;
}

.wpo-hero-slider .slide-inner:before,
.wpo-hero-slider .hero-inner:before,
.wpo-hero-slider-s2 .slide-inner:before,
.wpo-hero-slider-s2 .hero-inner:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  content: "";
  opacity: .7;
}

.wpo-hero-slider .slide-inner:after,
.wpo-hero-slider .hero-inner:after,
.wpo-hero-slider-s2 .slide-inner:after,
.wpo-hero-slider-s2 .hero-inner:after {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/slider/noise2.png);
  z-index: -1;
}

.wpo-hero-slider .gradient-overlay + .container,
.wpo-hero-slider-s2 .gradient-overlay + .container {
  position: relative;
  z-index: 11;
}

.wpo-hero-slider .swiper-slide,
.wpo-hero-slider-s2 .swiper-slide {
  position: relative;
  z-index: 11;
}

.wpo-hero-slider .slide-content,
.wpo-hero-slider-s2 .slide-content {
  max-width: 670px;
}

.wpo-hero-slider .slide-title h2,
.wpo-hero-slider-s2 .slide-title h2 {
  font-weight: 400;
  font-size: 100px;
  line-height: 100px;
  margin: 10px 0 15px;
  color: #fff;
}

@media (max-width: 1199px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 50px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 40px;
    line-height: 55px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 30px;
    line-height: 36px;
  }
}

.wpo-hero-slider .slide-title-sub,
.wpo-hero-slider-s2 .slide-title-sub {
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-title-sub,
  .wpo-hero-slider-s2 .slide-title-sub {
    max-width: 250px;
  }
}

.wpo-hero-slider .slide-title-sub:before,
.wpo-hero-slider-s2 .slide-title-sub:before {
  position: absolute;
  left: 0;
  top: -2px;
  content: "";
  width: 250px;
  height: 40px;
  background: -webkit-gradient(linear, left top, right top, from(#1C1817), to(rgba(28, 24, 23, 0)));
  background: linear-gradient(90deg, #1C1817 0%, rgba(28, 24, 23, 0) 100%);
  z-index: -1;
}

.wpo-hero-slider .slide-title-sub span,
.wpo-hero-slider-s2 .slide-title-sub span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  line-height: 30px;
  text-transform: capitalize;
  margin-left: 10px;
  color: #fff;
}

.wpo-hero-slider .slide-title-sub span i,
.wpo-hero-slider-s2 .slide-title-sub span i {
  color: #E5F346;
}

@media (max-width: 1399px) {
  .wpo-hero-slider .slide-title-sub span,
  .wpo-hero-slider-s2 .slide-title-sub span {
    font-size: 13px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-title-sub span,
  .wpo-hero-slider-s2 .slide-title-sub span {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-hero-slider .slide-title-sub span i,
.wpo-hero-slider-s2 .slide-title-sub span i {
  position: relative;
  top: 2px;
  margin-right: 3px;
}

.wpo-hero-slider .slide-title,
.wpo-hero-slider-s2 .slide-title {
  margin-top: 30px;
}

.wpo-hero-slider .slide-title h2,
.wpo-hero-slider-s2 .slide-title h2 {
  font-size: 80px;
  font-style: normal;
  font-weight: 500;
  line-height: 90px;
  text-transform: uppercase;
  color: #fff;
  margin-bottom: 30px;
}

@media (max-width: 1750px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 68px;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 55px;
    line-height: 70px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 46px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 40px;
    line-height: 50px;
  }
}

@media (max-width: 575px) {
  .wpo-hero-slider .slide-title h2,
  .wpo-hero-slider-s2 .slide-title h2 {
    font-size: 30px;
    line-height: 40px;
  }
}

.wpo-hero-slider .swiper-button-prev,
.wpo-hero-slider .swiper-button-next,
.wpo-hero-slider-s2 .swiper-button-prev,
.wpo-hero-slider-s2 .swiper-button-next {
  background: rgba(255, 255, 255, 0.2);
  width: 60px;
  height: 60px;
  line-height: 60px;
  border: 2px solid #fff;
  border-radius: 50%;
  opacity: 0.7;
  text-align: center;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  display: none;
}

.wpo-hero-slider .swiper-button-prev:hover,
.wpo-hero-slider .swiper-button-next:hover,
.wpo-hero-slider-s2 .swiper-button-prev:hover,
.wpo-hero-slider-s2 .swiper-button-next:hover {
  opacity: 0.9;
}

@media (max-width: 767px) {
  .wpo-hero-slider .swiper-button-prev,
  .wpo-hero-slider .swiper-button-next,
  .wpo-hero-slider-s2 .swiper-button-prev,
  .wpo-hero-slider-s2 .swiper-button-next {
    display: none;
  }
}

.wpo-hero-slider .swiper-button-prev,
.wpo-hero-slider-s2 .swiper-button-prev {
  left: 25px;
}

.wpo-hero-slider .swiper-button-prev:before,
.wpo-hero-slider-s2 .swiper-button-prev:before {
  font-family: "themify";
  content: "\e629";
  font-size: 20px;
  color: #fff;
}

.wpo-hero-slider .swiper-button-next,
.wpo-hero-slider-s2 .swiper-button-next {
  right: 25px;
}

.wpo-hero-slider .swiper-button-next:before,
.wpo-hero-slider-s2 .swiper-button-next:before {
  font-family: "themify";
  content: "\e628";
  font-size: 20px;
  color: #fff;
}

@media (max-width: 991px) {
  .wpo-hero-slider .swiper-button-prev,
  .wpo-hero-slider .swiper-button-next,
  .wpo-hero-slider-s2 .swiper-button-prev,
  .wpo-hero-slider-s2 .swiper-button-next {
    display: none;
  }
}

.wpo-hero-slider .swiper-pagination,
.wpo-hero-slider-s2 .swiper-pagination {
  right: 300px;
  top: 60%;
  left: auto;
  width: unset;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media (max-width: 1600px) {
  .wpo-hero-slider .swiper-pagination,
  .wpo-hero-slider-s2 .swiper-pagination {
    right: 60px;
  }
}

@media (max-width: 575px) {
  .wpo-hero-slider .swiper-pagination,
  .wpo-hero-slider-s2 .swiper-pagination {
    right: 20px;
  }
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet,
.wpo-hero-slider-s2 .swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  text-align: center;
  line-height: 8px;
  font-size: 18px;
  color: #6A6A6A;
  opacity: 1;
  display: block;
  background: none;
  background: #fff;
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.wpo-hero-slider-s2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  color: #fff;
  position: relative;
  background: #E5F346;
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.wpo-hero-slider-s2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  position: absolute;
  left: -5px;
  top: -5px;
  width: 18px;
  height: 18px;
  border: 1px solid #E5F346;
  content: "";
  border-radius: 50%;
}

.wpo-hero-slider .swiper-container-horizontal > .swiper-pagination-bullets,
.wpo-hero-slider .swiper-pagination-custom,
.wpo-hero-slider .swiper-pagination-fraction,
.wpo-hero-slider-s2 .swiper-container-horizontal > .swiper-pagination-bullets,
.wpo-hero-slider-s2 .swiper-pagination-custom,
.wpo-hero-slider-s2 .swiper-pagination-fraction {
  bottom: 30px;
}

.wpo-hero-slider .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.wpo-hero-slider-s2 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 20px 0px;
}

.wpo-hero-slider .hero-right-video,
.wpo-hero-slider-s2 .hero-right-video {
  background: none;
  padding: 30px 0;
  position: absolute;
  right: 475px;
  top: 90px;
  z-index: 11;
  height: 757px;
  width: 308px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (max-width: 1600px) {
  .wpo-hero-slider .hero-right-video,
  .wpo-hero-slider-s2 .hero-right-video {
    right: 164px;
    width: 290px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider .hero-right-video,
  .wpo-hero-slider-s2 .hero-right-video {
    right: 153px;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider .hero-right-video,
  .wpo-hero-slider-s2 .hero-right-video {
    right: 140px;
    width: 250px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .hero-right-video,
  .wpo-hero-slider-s2 .hero-right-video {
    display: none;
  }
}

.wpo-hero-slider .hero-right-video::before,
.wpo-hero-slider-s2 .hero-right-video::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  opacity: 0.25;
  background: -webkit-gradient(linear, left top, left bottom, from(#E5F346), to(rgba(229, 243, 70, 0)));
  background: linear-gradient(180deg, #E5F346 0%, rgba(229, 243, 70, 0) 100%);
}

.wpo-hero-slider .hero-right-video .hero-right-video-wrap,
.wpo-hero-slider-s2 .hero-right-video .hero-right-video-wrap {
  position: relative;
  width: 135px;
  height: 119px;
  margin: 0 auto;
}

.wpo-hero-slider .hero-right-video p,
.wpo-hero-slider-s2 .hero-right-video p {
  font-size: 13px;
  font-family: "Epilogue";
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  color: #fff;
}

.wpo-hero-slider .hero-right-video a,
.wpo-hero-slider-s2 .hero-right-video a {
  width: 68px;
  height: 68px;
  display: block;
  border-radius: 68px;
  background: #E5F346;
  position: relative;
  margin: 0 auto;
  top: -30px;
}

.wpo-hero-slider .hero-right-video a:before,
.wpo-hero-slider-s2 .hero-right-video a:before {
  content: "";
  width: 0px;
  height: 0px;
  border-top: 9px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #1C1817;
  position: absolute;
  left: 52%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.wpo-hero-slider .hero-right-video a:after,
.wpo-hero-slider-s2 .hero-right-video a:after {
  position: absolute;
  left: -10px;
  top: -10px;
  width: 88px;
  height: 88px;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  content: "";
  border-radius: 50%;
}

/*-------------------------------------------
	wpo-hero-slider-1
--------------------------------------------*/
.wpo-hero-slider-1 {
  height: 960px;
  position: relative;
  border-top: 1px solid #D9D9D9;
  background: #F5F6E4;
  z-index: 1;
  padding-top: 120px;
  overflow: hidden;
  z-index: 1;
  /** slider controls **/
}

.wpo-hero-slider-1 .noise {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: .03;
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 {
    height: 750px;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 {
    height: 700px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 {
    height: 650px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 {
    height: 530px;
    padding-top: 60px;
  }
}

.wpo-hero-slider-1 .shape {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.wpo-hero-slider-1 .hero-slider {
  height: 840px;
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .hero-slider {
    height: 630px;
  }
}

.wpo-hero-slider-1 .slide {
  height: 100%;
}

.wpo-hero-slider-1 .slide-left {
  position: relative;
}

.wpo-hero-slider-1 .slide-caption {
  margin-top: 160px;
  z-index: 1;
  padding-left: 290px;
}

@media (max-width: 1750px) {
  .wpo-hero-slider-1 .slide-caption {
    margin-top: 100px;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .slide-caption {
    margin-top: 50px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-caption {
    padding-left: 245px;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 .slide-caption {
    padding-left: 0;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 .slide-caption {
    margin-top: 40px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-caption {
    margin-top: 0px;
  }
}

.wpo-hero-slider-1 .slide-text {
  position: relative;
  padding-top: 50px;
  max-width: 650px;
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-text {
    padding-top: 20px;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 .slide-text {
    max-width: 440px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 .slide-text {
    max-width: 440px;
    margin: 0 auto;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-text {
    max-width: 100%;
    text-align: center;
    padding-top: 0;
  }
}

.wpo-hero-slider-1 .slide-text .hero-left-img {
  max-width: 244px;
  display: inline-block;
  position: absolute;
  right: 80px;
  top: 0px;
  z-index: 11;
}

@media (max-width: 1750px) {
  .wpo-hero-slider-1 .slide-text .hero-left-img {
    max-width: 200px;
    top: 30px;
    right: 30px;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .slide-text .hero-left-img {
    max-width: 150px;
    top: 30px;
    right: 0px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-text .hero-left-img {
    max-width: 100px;
    top: 30px;
    right: 0px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-text .hero-left-img {
    position: relative;
  }
}

.wpo-hero-slider-1 .slide-text .hero-left-img img {
  width: 100%;
}

.wpo-hero-slider-1 .slide-title-sub {
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-title-sub {
    max-width: 250px;
    margin: 0 auto;
  }
}

.wpo-hero-slider-1 .slide-title-sub:before {
  position: absolute;
  left: 0;
  top: -2px;
  content: "";
  width: 250px;
  height: 40px;
  background: -webkit-gradient(linear, left top, right top, from(#E5F346), to(rgba(255, 255, 255, 0)));
  background: linear-gradient(90deg, #E5F346 0%, rgba(255, 255, 255, 0) 100%);
  z-index: -1;
}

.wpo-hero-slider-1 .slide-title-sub span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  line-height: 30px;
  text-transform: capitalize;
  margin-left: 10px;
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-title-sub span {
    font-size: 13px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-title-sub span {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-hero-slider-1 .slide-title-sub span i {
  position: relative;
  top: 2px;
  margin-right: 3px;
}

.wpo-hero-slider-1 .slide-title {
  margin-top: 30px;
}

.wpo-hero-slider-1 .slide-title h2 {
  font-size: 80px;
  font-style: normal;
  font-weight: 500;
  line-height: 90px;
  text-transform: uppercase;
  color: #1C1817;
}

@media (max-width: 1750px) {
  .wpo-hero-slider-1 .slide-title h2 {
    font-size: 68px;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .slide-title h2 {
    font-size: 55px;
    line-height: 70px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-title h2 {
    font-size: 46px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 .slide-title h2 {
    font-size: 40px;
    line-height: 50px;
  }
}

@media (max-width: 575px) {
  .wpo-hero-slider-1 .slide-title h2 {
    font-size: 30px;
    line-height: 40px;
  }
}

.wpo-hero-slider-1 .slide-title h2 small {
  display: block;
  font-size: 80px;
}

@media (max-width: 1750px) {
  .wpo-hero-slider-1 .slide-title h2 small {
    font-size: 68px;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .slide-title h2 small {
    font-size: 55px;
    line-height: 70px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slide-title h2 small {
    font-size: 46px;
  }
}

@media (max-width: 575px) {
  .wpo-hero-slider-1 .slide-title h2 small {
    font-size: 30px;
    line-height: 40px;
  }
}

.wpo-hero-slider-1 .slide-title h2 span {
  position: relative;
  z-index: 1;
}

.wpo-hero-slider-1 .slide-title h2 span::before {
  position: absolute;
  left: 5px;
  bottom: 19px;
  width: 96%;
  height: 19px;
  background: #E5F346;
  content: "";
  z-index: -1;
}

.wpo-hero-slider-1 .slide-subtitle p {
  font-family: "Epilogue";
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  color: #525252;
  margin-bottom: 30px;
  margin-right: 130px;
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 .slide-subtitle p {
    margin-bottom: 15px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 .slide-subtitle p {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slide-subtitle p {
    margin-right: 30px;
  }
}

.wpo-hero-slider-1 .hero-btn {
  padding: 15px 20px;
  background: #E5F346;
  display: inline-block;
  border: 1px solid #1C1817;
  width: 290px;
  margin-top: 20px;
  position: relative;
}

.wpo-hero-slider-1 .hero-btn span {
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  color: #1C1817;
  font-family: "Epilogue";
}

.wpo-hero-slider-1 .hero-btn p {
  margin-bottom: 0;
  font-size: 29px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: uppercase;
  font-family: "Epilogue";
  color: #1C1817;
  margin-top: 6px;
}

.wpo-hero-slider-1 .hero-btn i {
  position: absolute;
  right: 25px;
  top: 25px;
  width: 45px;
  height: 45px;
  background: #1C1817;
  border-radius: 50%;
  text-align: center;
  line-height: 45px;
  color: #fff;
}

.wpo-hero-slider-1 .hero-btn i:before {
  position: relative;
  left: -2px;
}

.wpo-hero-slider-1 .slider-pic {
  position: absolute;
  right: -15px;
  bottom: 0;
  max-width: 1160px;
}

@media (max-width: 1799px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 1000px;
  }
}

@media (max-width: 1699px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 900px;
  }
}

@media (max-width: 1599px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 800px;
  }
}

@media (max-width: 1499px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 700px;
  }
}

@media (max-width: 1399px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 700px;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 700px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider-1 .slider-pic {
    max-width: 550px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slider-pic {
    display: none;
  }
}

.wpo-hero-slider-1 .slider-pic .slider-item img {
  width: unset;
  margin-left: auto;
}

.wpo-hero-slider-1 .slider-pic .slick-track {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.wpo-hero-slider-1 .slick-dots {
  width: 0;
  top: auto;
  bottom: -100px;
  left: 32%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 1699px) {
  .wpo-hero-slider-1 .slick-dots {
    left: 37%;
  }
}

@media (max-width: 1600px) {
  .wpo-hero-slider-1 .slick-dots {
    left: 40%;
  }
}

@media (max-width: 1500px) {
  .wpo-hero-slider-1 .slick-dots {
    left: 43%;
  }
}

@media (max-width: 1199px) {
  .wpo-hero-slider-1 .slick-dots {
    left: 15px;
    bottom: -70px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-1 .slick-dots {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
    left: 0;
  }
}

.wpo-hero-slider-1 .slick-dots li {
  margin: 0;
}

.wpo-hero-slider-1 .slick-dots li + li {
  margin-left: 20px;
}

.wpo-hero-slider-1 .slick-dots li button {
  font-size: 0px;
  background: #1C1817;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.wpo-hero-slider-1 .slick-dots li.slick-active button {
  color: #1C1817;
}

.wpo-hero-slider-1 .slick-dots li.slick-active {
  color: #1C1817;
  position: relative;
}

.wpo-hero-slider-1 .slick-dots li.slick-active::before {
  position: absolute;
  left: -7px;
  top: -7px;
  width: 24px;
  height: 24px;
  border: 1px solid #1C1817;
  content: "";
  border-radius: 50%;
}

.wpo-hero-slider-1 .slick-dots button:before,
.wpo-hero-slider-1 .slick-dots button:before {
  display: none;
}

.hero-left-content {
  position: absolute;
  left: 0;
  top: 0;
  width: 220px;
  text-align: center;
  z-index: 1;
}

@media (max-width: 1199px) {
  .hero-left-content {
    top: auto;
    bottom: 0;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
  }
}

@media (max-width: 767px) {
  .hero-left-content {
    height: auto;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

@media (max-width: 575px) {
  .hero-left-content {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.hero-left-content .hero-left-content-inner {
  background: rgba(28, 24, 23, 0.09);
  padding: 100px 0;
}

@media (max-width: 1600px) {
  .hero-left-content .hero-left-content-inner {
    padding: 40px 0;
  }
}

@media (max-width: 1199px) {
  .hero-left-content .hero-left-content-inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 30px;
    height: 130px;
    -ms-flex-preferred-size: 90%;
        flex-basis: 90%;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

@media (max-width: 575px) {
  .hero-left-content .hero-left-content-inner {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
  }
}

@media (max-width: 767px) {
  .hero-left-content .hero-left-content-inner {
    padding: 30px 10px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    height: auto;
  }
}

.hero-left-content .hero-left-content-inner .hero-left-content-item {
  margin-bottom: 50px;
}

@media (max-width: 1600px) {
  .hero-left-content .hero-left-content-inner .hero-left-content-item {
    margin-bottom: 25px;
  }
}

@media (max-width: 1199px) {
  .hero-left-content .hero-left-content-inner .hero-left-content-item {
    margin: 0 20px;
  }
}

@media (max-width: 767px) {
  .hero-left-content .hero-left-content-inner .hero-left-content-item {
    margin: 10px 10px;
  }
}

.hero-left-content .hero-left-content-inner .hero-left-content-item:last-child {
  margin-bottom: 0;
}

.hero-left-content .hero-left-content-inner .hero-left-content-item h2 {
  font-size: 42px;
  font-style: normal;
  font-weight: 600;
}

@media (max-width: 991px) {
  .hero-left-content .hero-left-content-inner .hero-left-content-item h2 {
    font-size: 25px;
  }
}

.hero-left-content .hero-left-content-inner .hero-left-content-item span {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
}

@media (max-width: 991px) {
  .hero-left-content .hero-left-content-inner .hero-left-content-item span {
    font-size: 13px;
  }
}

.hero-left-content .hero-left-video {
  background: #fff;
  padding: 30px 0;
  position: relative;
}

@media (max-width: 1199px) {
  .hero-left-content .hero-left-video {
    padding: 20px 0;
    height: 130px;
  }
}

.hero-left-content .hero-left-video .hero-left-video-wrap {
  position: relative;
  width: 135px;
  height: 119px;
  margin: 0 auto;
}

.hero-left-content .hero-left-video p {
  font-size: 13px;
  font-family: "Epilogue";
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  color: #1C1817;
}

.hero-left-content .hero-left-video a {
  width: 68px;
  height: 68px;
  display: block;
  border-radius: 68px;
  background: #1C1817;
  position: relative;
  margin: 0 auto;
  top: -30px;
}

.hero-left-content .hero-left-video a:before {
  content: "";
  width: 0px;
  height: 0px;
  border-top: 9px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #E5F346;
  position: absolute;
  left: 52%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.hero-left-content .hero-left-video a:after {
  position: absolute;
  left: -10px;
  top: -10px;
  width: 88px;
  height: 88px;
  border: 1px dashed rgba(28, 24, 23, 0.3);
  content: "";
  border-radius: 50%;
}

/*-------------------------------------------
	wpo-hero-slider-s2
--------------------------------------------*/
.wpo-hero-slider-s2 .slide-content {
  padding-top: 90px;
}

@media (max-width: 767px) {
  .wpo-hero-slider-s2 .slide-content {
    padding-top: 0px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider-s2 .slide-content .slide-title-sub {
    margin: 0;
  }
  .wpo-hero-slider-s2 .slide-content .slide-title-sub span {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}

/*--------------------------------------------------------------
4. wpo-footer
--------------------------------------------------------------*/
.wpo-site-footer,
.wpo-site-footer-s2 {
  background: #1C1817;
  position: relative;
  font-size: 15px;
  overflow: hidden;
  z-index: 1;
  /*** newsletter-widget ***/
}

.wpo-site-footer:before,
.wpo-site-footer-s2:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-site-footer ul,
.wpo-site-footer-s2 ul {
  list-style: none;
}

.wpo-site-footer p,
.wpo-site-footer-s2 p {
  color: rgba(255, 255, 255, 0.6);
}

.wpo-site-footer li,
.wpo-site-footer-s2 li {
  color: rgba(255, 255, 255, 0.6);
}

.wpo-site-footer .container,
.wpo-site-footer-s2 .container {
  position: relative;
}

.wpo-site-footer .wpo-upper-footer,
.wpo-site-footer-s2 .wpo-upper-footer {
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.wpo-site-footer .wpo-upper-footer .navbar-brand,
.wpo-site-footer-s2 .wpo-upper-footer .navbar-brand {
  color: #fff;
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-upper-footer,
  .wpo-site-footer-s2 .wpo-upper-footer {
    padding: 50px 0 0;
  }
}

.wpo-site-footer .wpo-upper-footer .col:nth-child(2) .widget,
.wpo-site-footer-s2 .wpo-upper-footer .col:nth-child(2) .widget {
  position: relative;
}

.wpo-site-footer .wpo-upper-footer .col:nth-child(2) .widget::before,
.wpo-site-footer-s2 .wpo-upper-footer .col:nth-child(2) .widget::before {
  position: absolute;
  left: -70px;
  top: 0;
  width: 1px;
  height: 100%;
  content: "";
  background: rgba(255, 255, 255, 0.15);
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-upper-footer .col:nth-child(2) .widget::before,
  .wpo-site-footer-s2 .wpo-upper-footer .col:nth-child(2) .widget::before {
    display: none;
  }
}

.wpo-site-footer .wpo-upper-footer .col:nth-child(2) .widget::after,
.wpo-site-footer-s2 .wpo-upper-footer .col:nth-child(2) .widget::after {
  position: absolute;
  right: -40px;
  top: 0;
  width: 1px;
  height: 100%;
  content: "";
  background: rgba(255, 255, 255, 0.15);
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-upper-footer .col:nth-child(2) .widget::after,
  .wpo-site-footer-s2 .wpo-upper-footer .col:nth-child(2) .widget::after {
    display: none;
  }
}

.wpo-site-footer .widget,
.wpo-site-footer-s2 .widget {
  padding-top: 120px;
  padding-bottom: 60px;
}

@media (max-width: 991px) {
  .wpo-site-footer .widget,
  .wpo-site-footer-s2 .widget {
    padding: 0;
    margin-bottom: 50px;
  }
}

.wpo-site-footer .widget-title,
.wpo-site-footer-s2 .widget-title {
  margin-bottom: 40px;
}

@media (max-width: 767px) {
  .wpo-site-footer .widget-title,
  .wpo-site-footer-s2 .widget-title {
    margin-bottom: 20px;
  }
}

.wpo-site-footer .widget-title h3,
.wpo-site-footer-s2 .widget-title h3 {
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: 45px;
  text-transform: uppercase;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-site-footer .widget-title h3,
  .wpo-site-footer-s2 .widget-title h3 {
    font-size: 25px;
  }
}

.wpo-site-footer .about-widget ul,
.wpo-site-footer-s2 .about-widget ul {
  overflow: hidden;
  max-width: 250px;
}

.wpo-site-footer .about-widget ul li,
.wpo-site-footer-s2 .about-widget ul li {
  font-weight: 500;
  font-size: 16px;
  line-height: 160.5%;
  float: left;
}

.wpo-site-footer .about-widget ul li + li,
.wpo-site-footer-s2 .about-widget ul li + li {
  margin-top: 10px;
}

.wpo-site-footer .contact-ft ul li,
.wpo-site-footer-s2 .contact-ft ul li {
  padding-bottom: 15px;
  position: relative;
  padding-left: 35px;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  text-transform: capitalize;
  color: #fff;
}

.wpo-site-footer .contact-ft ul li i,
.wpo-site-footer-s2 .contact-ft ul li i {
  position: absolute;
  left: 0;
  top: 0;
  color: #E5F346;
}

.wpo-site-footer .contact-ft ul li .fi:before,
.wpo-site-footer-s2 .contact-ft ul li .fi:before {
  font-size: 20px;
  margin-right: 15px;
}

.wpo-site-footer .newsletter-widget p,
.wpo-site-footer-s2 .newsletter-widget p {
  font-size: 16px;
}

.wpo-site-footer .newsletter-widget form,
.wpo-site-footer-s2 .newsletter-widget form {
  margin-top: 25px;
  position: relative;
  max-width: 380px;
}

.wpo-site-footer .newsletter-widget form input,
.wpo-site-footer-s2 .newsletter-widget form input {
  background-color: transparent;
  height: 50px;
  color: #fff;
  padding: 6px 15px;
  border: 1px solid rgba(255, 255, 255, 0.22);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 0;
}

.wpo-site-footer .newsletter-widget form input::-webkit-input-placeholder,
.wpo-site-footer-s2 .newsletter-widget form input::-webkit-input-placeholder {
  color: #fff;
}

.wpo-site-footer .newsletter-widget form input:-ms-input-placeholder,
.wpo-site-footer-s2 .newsletter-widget form input:-ms-input-placeholder {
  color: #fff;
}

.wpo-site-footer .newsletter-widget form input::-ms-input-placeholder,
.wpo-site-footer-s2 .newsletter-widget form input::-ms-input-placeholder {
  color: #fff;
}

.wpo-site-footer .newsletter-widget form input::placeholder,
.wpo-site-footer-s2 .newsletter-widget form input::placeholder {
  color: #fff;
}

.wpo-site-footer .newsletter-widget form .submit,
.wpo-site-footer-s2 .newsletter-widget form .submit {
  margin-top: 20px;
}

.wpo-site-footer .newsletter-widget form .submit button,
.wpo-site-footer-s2 .newsletter-widget form .submit button {
  background: #E5F346;
  border: 0;
  outline: 0;
  color: #1C1817;
  height: 50px;
  width: 202px;
  line-height: 50px;
  font-weight: 700;
  font-size: 12px;
  line-height: 95%;
  text-transform: uppercase;
}

.wpo-site-footer .wpo-middle-footer,
.wpo-site-footer-s2 .wpo-middle-footer {
  padding: 60px 0;
}

.wpo-site-footer .wpo-middle-footer h2,
.wpo-site-footer-s2 .wpo-middle-footer h2 {
  color: #fff;
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: 45px;
  text-transform: uppercase;
  font-family: "Sora", sans-serif;
  margin-bottom: 60px;
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-middle-footer h2,
  .wpo-site-footer-s2 .wpo-middle-footer h2 {
    margin-bottom: 30px;
    font-size: 25px;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer .wpo-middle-footer h2,
  .wpo-site-footer-s2 .wpo-middle-footer h2 {
    margin-bottom: 30px;
    font-size: 25px;
  }
}

@media (max-width: 575px) {
  .wpo-site-footer .wpo-middle-footer h2,
  .wpo-site-footer-s2 .wpo-middle-footer h2 {
    font-size: 20px;
  }
}

.wpo-site-footer .wpo-middle-footer .instagram-widget,
.wpo-site-footer-s2 .wpo-middle-footer .instagram-widget {
  text-align: center;
}

.wpo-site-footer .instagram-wrap,
.wpo-site-footer-s2 .instagram-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 767px) {
  .wpo-site-footer .instagram-wrap,
  .wpo-site-footer-s2 .instagram-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-site-footer .instagram-wrap .instagram-item,
.wpo-site-footer-s2 .instagram-wrap .instagram-item {
  position: relative;
  margin: 0 16px;
}

@media (max-width: 1199px) {
  .wpo-site-footer .instagram-wrap .instagram-item,
  .wpo-site-footer-s2 .instagram-wrap .instagram-item {
    margin: 0 8px;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer .instagram-wrap .instagram-item,
  .wpo-site-footer-s2 .instagram-wrap .instagram-item {
    -ms-flex-preferred-size: 18%;
        flex-basis: 18%;
    margin: 0 5px;
    margin-bottom: 10px;
  }
}

@media (max-width: 575px) {
  .wpo-site-footer .instagram-wrap .instagram-item,
  .wpo-site-footer-s2 .instagram-wrap .instagram-item {
    -ms-flex-preferred-size: 16%;
        flex-basis: 16%;
  }
}

.wpo-site-footer .instagram-wrap .instagram-item i,
.wpo-site-footer-s2 .instagram-wrap .instagram-item i {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(229, 243, 70, 0.8);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  color: #1C1817;
  font-size: 20px;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-site-footer .instagram-wrap .instagram-item:hover i,
.wpo-site-footer-s2 .instagram-wrap .instagram-item:hover i {
  -webkit-transform: scale(1);
          transform: scale(1);
}

.wpo-site-footer .wpo-lower-footer,
.wpo-site-footer-s2 .wpo-lower-footer {
  text-align: left;
  border-top: 1px solid rgba(255, 255, 255, 0.22);
  padding: 30px 0;
}

.wpo-site-footer .wpo-lower-footer ul.copyright,
.wpo-site-footer-s2 .wpo-lower-footer ul.copyright {
  text-align: right;
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-lower-footer ul.copyright,
  .wpo-site-footer-s2 .wpo-lower-footer ul.copyright {
    text-align: center;
  }
}

.wpo-site-footer .wpo-lower-footer ul.copyright li,
.wpo-site-footer-s2 .wpo-lower-footer ul.copyright li {
  color: #fff;
  text-transform: uppercase;
}

@media (max-width: 767px) {
  .wpo-site-footer .wpo-lower-footer ul.copyright li,
  .wpo-site-footer-s2 .wpo-lower-footer ul.copyright li {
    font-size: 14px;
  }
}

.wpo-site-footer .wpo-lower-footer ul.copyright li a,
.wpo-site-footer-s2 .wpo-lower-footer ul.copyright li a {
  color: #E5F346;
}

.wpo-site-footer .social ul,
.wpo-site-footer-s2 .social ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 991px) {
  .wpo-site-footer .social ul,
  .wpo-site-footer-s2 .social ul {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-bottom: 20px;
  }
}

.wpo-site-footer .social ul li + li,
.wpo-site-footer-s2 .social ul li + li {
  margin-left: 10px;
}

.wpo-site-footer .social ul li a,
.wpo-site-footer-s2 .social ul li a {
  color: #fff;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border: 1px solid rgba(255, 255, 255, 0.28);
  display: block;
  text-align: center;
  font-size: 16px;
}

.wpo-site-footer .social ul li a:hover,
.wpo-site-footer-s2 .social ul li a:hover {
  background: #E5F346;
}

.wpo-site-footer .social ul li a .fi:before,
.wpo-site-footer-s2 .social ul li a .fi:before {
  color: rgba(255, 255, 255, 0.72);
  font-size: 14px;
}

.wpo-site-footer-s2 .widget {
  padding: 100px 0;
}

@media (max-width: 991px) {
  .wpo-site-footer-s2 .widget {
    padding: 30px 0;
    margin-bottom: 0;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .widget {
    padding-top: 0;
  }
}

.wpo-site-footer-s2 .widget::before {
  display: none;
}

.wpo-site-footer-s2 .social {
  margin-top: 70px;
}

@media (max-width: 991px) {
  .wpo-site-footer-s2 .social ul {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .social ul {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .newsletter-widget {
    padding-top: 0;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .newsletter-widget form {
    margin: 0 auto;
  }
}

.wpo-site-footer-s2 .wpo-upper-footer {
  padding-left: 280px;
  border: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.28);
}

@media (max-width: 1700px) {
  .wpo-site-footer-s2 .wpo-upper-footer {
    padding-left: 100px;
  }
}

@media (max-width: 1500px) {
  .wpo-site-footer-s2 .wpo-upper-footer {
    padding-left: 100px;
  }
}

@media (max-width: 1399px) {
  .wpo-site-footer-s2 .wpo-upper-footer {
    padding-left: 50px;
  }
}

@media (max-width: 1199px) {
  .wpo-site-footer-s2 .wpo-upper-footer {
    padding-left: 0px;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .wpo-upper-footer {
    text-align: center;
  }
}

.wpo-site-footer-s2 .wpo-upper-footer h2 {
  max-width: 420px;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .wpo-upper-footer h2 {
    max-width: 100%;
  }
}

.wpo-site-footer-s2 .instagram-wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: -5px;
  max-width: 320px;
}

@media (max-width: 767px) {
  .wpo-site-footer-s2 .instagram-wrap {
    margin: 0 auto;
  }
}

.wpo-site-footer-s2 .instagram-wrap .instagram-item {
  margin: 5px;
  -ms-flex-preferred-size: 29.33%;
      flex-basis: 29.33%;
}

.wpo-site-footer-s2 .instagram-wrap .instagram-item img {
  width: 100%;
}

.wpo-site-footer-s2 .wpo-lower-footer {
  padding-left: 280px;
  border-right: 1px solid rgba(255, 255, 255, 0.28);
}

@media (max-width: 1700px) {
  .wpo-site-footer-s2 .wpo-lower-footer {
    padding-left: 100px;
  }
}

@media (max-width: 1500px) {
  .wpo-site-footer-s2 .wpo-lower-footer {
    padding-left: 100px;
  }
}

@media (max-width: 1399px) {
  .wpo-site-footer-s2 .wpo-lower-footer {
    padding-left: 50px;
  }
}

@media (max-width: 1199px) {
  .wpo-site-footer-s2 .wpo-lower-footer {
    padding-left: 15px;
  }
}

.wpo-site-footer-s2 .wpo-lower-footer ul.copyright {
  text-align: left;
}

@media (max-width: 991px) {
  .wpo-site-footer-s2 .wpo-lower-footer ul.copyright {
    text-align: center;
  }
}

.wpo-site-footer-s2 .wpo-team-section {
  background: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 80px 0 70px;
}

@media (max-width: 991px) {
  .wpo-site-footer-s2 .wpo-team-section {
    border-bottom: 1px solid rgba(255, 255, 255, 0.28);
  }
}

.wpo-site-footer-s2 .wpo-team-section::before, .wpo-site-footer-s2 .wpo-team-section::after {
  display: none;
}

.wpo-site-footer-s2 .wpo-team-section h2 {
  text-align: center;
  color: #fff;
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: 45px;
  text-transform: uppercase;
  margin-bottom: 40px;
}

@media (max-width: 1500px) {
  .wpo-site-footer-s2 .wpo-team-section h2 {
    font-size: 25px;
  }
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-] {
  display: none;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item {
  padding: 0;
  background: none;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content p {
  color: #fff;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content span {
  color: #E5F346;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content h3 {
  color: #fff;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content small {
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  color: #8F8F8F;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content small b {
  color: #E5F346;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .owl-dots {
  display: block;
  margin-top: 25px;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .owl-dots .owl-dot {
  background: #8F8F8F;
}

.wpo-site-footer-s2 .wpo-team-section .wpo-team-wrap-s2 .owl-dots .owl-dot.active {
  background-color: #E5F346;
}

.sticky-header {
  width: 100%;
  position: fixed;
  left: 0;
  top: -200px;
  z-index: 9999;
  opacity: 0;
  -webkit-transition: all 0.7s;
  transition: all 0.7s;
}

.sticky-on {
  opacity: 1;
  top: 0;
}

/* 3.2 wpo-about-section */
.wpo-about-section .wpo-about-wrap {
  margin-top: -130px;
}

.wpo-about-section .wpo-about-wrap .row {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap {
    margin-top: 0;
  }
}

.wpo-about-section .wpo-about-wrap .about-content {
  max-width: 340px;
  padding: 50px 45px;
  background: #1C1817;
  position: relative;
  overflow: hidden;
}

@media (max-width: 1199px) {
  .wpo-about-section .wpo-about-wrap .about-content {
    padding: 40px 25px;
  }
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .about-content {
    max-width: 100%;
    text-align: center;
  }
}

.wpo-about-section .wpo-about-wrap .about-content:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
}

.wpo-about-section .wpo-about-wrap .about-content h3 {
  font-family: "Sora", sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  text-transform: uppercase;
  color: #fff;
  margin-bottom: 15px;
}

.wpo-about-section .wpo-about-wrap .about-content p {
  margin-bottom: 0;
  color: #fff;
  font-size: 15px;
}

.wpo-about-section .wpo-about-wrap .about-content p span {
  color: #E5F346;
  text-decoration: underline;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .about-img {
    text-align: center;
    margin: 30px 0;
  }
}

.wpo-about-section .wpo-about-wrap .col {
  position: relative;
}

.wpo-about-section .wpo-about-wrap .col:first-child:before {
  position: absolute;
  left: -20px;
  top: -30px;
  width: 100px;
  height: 100px;
  content: "";
  background: #F5F6E4;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) {
  z-index: 1;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) .about-content {
  margin-left: auto;
  background: #F5F6E4;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) .about-content h3 {
  color: #1C1817;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) .about-content p {
  color: #424740;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) .about-content p span {
  color: #1C1817;
  font-weight: 500;
}

.wpo-about-section .wpo-about-wrap .col:nth-child(3) .ab-right {
  position: absolute;
  right: -90px;
  top: -90px;
  z-index: -1;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .col:nth-child(3) .ab-right {
    display: none;
  }
}

/* 3.3 wpo-payment-section */
.wpo-payment-section,
.wpo-payment-section-s2 {
  position: relative;
  z-index: 1;
}

.wpo-payment-section:before,
.wpo-payment-section-s2:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-payment-section:after,
.wpo-payment-section-s2:after {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: url(../images/payment-bg.png), #E5F346 50%/cover no-repeat;
  z-index: -2;
  content: "";
  background-repeat: no-repeat;
  background-size: cover;
}

.wpo-payment-section .wpo-payment-wrap .wpo-section-title,
.wpo-payment-section-s2 .wpo-payment-wrap .wpo-section-title {
  text-align: left;
  max-width: 300px;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .wpo-payment-section .wpo-payment-wrap .wpo-section-title,
  .wpo-payment-section-s2 .wpo-payment-wrap .wpo-section-title {
    text-align: center;
    margin: 0 auto;
    margin-bottom: 30px;
  }
}

.wpo-payment-section .wpo-payment-wrap .wpo-section-title span:before,
.wpo-payment-section-s2 .wpo-payment-wrap .wpo-section-title span:before {
  background-color: #fff;
}

.wpo-payment-section .wpo-payment-wrap .wpo-section-title small,
.wpo-payment-section-s2 .wpo-payment-wrap .wpo-section-title small {
  background: rgba(28, 24, 23, 0.12);
  color: #1C1817;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -20px;
}

@media (max-width: 575px) {
  .wpo-payment-section .wpo-payment-wrap .payment-form-area,
  .wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area {
    display: block;
  }
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group {
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
  margin-bottom: 30px;
  padding: 0 20px;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group:last-child,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group:last-child {
  margin-bottom: 0;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group label,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group label {
  display: block;
  font-family: "Epilogue";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  color: #1C1817;
  margin-bottom: 11px;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group input,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group input {
  width: 100%;
  width: 100%;
  padding: 15px;
  height: 65px;
  background: transparent;
  border: 1px solid #1C1817;
  font-size: 17px;
  font-style: normal;
  font-weight: 500;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group input:focus,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group input:focus {
  outline: none;
}

.wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group .theme-btn, .wpo-payment-section .wpo-payment-wrap .payment-form-area .form-group .view-cart-btn,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group .theme-btn,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group .view-cart-btn {
  background: #1C1817;
  color: #fff;
  text-transform: capitalize;
  margin-top: 20px;
}

.wpo-payment-section .wpo-payment-wrap .payment-result,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-result {
  max-width: 226px;
  margin-left: auto;
  background: #fff;
  text-align: center;
  border: 1px solid var(--black, #1C1817);
  padding: 30px;
}

@media (max-width: 991px) {
  .wpo-payment-section .wpo-payment-wrap .payment-result,
  .wpo-payment-section-s2 .wpo-payment-wrap .payment-result {
    margin: 0;
    max-width: 100%;
  }
}

.wpo-payment-section .wpo-payment-wrap .payment-result .result-item,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item {
  margin-bottom: 40px;
}

.wpo-payment-section .wpo-payment-wrap .payment-result .result-item:last-child,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item:last-child {
  margin-bottom: 0;
}

.wpo-payment-section .wpo-payment-wrap .payment-result .result-item span,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item span {
  font-family: "Epilogue";
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
}

.wpo-payment-section .wpo-payment-wrap .payment-result .result-item h3,
.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  margin-top: 10px;
}

/* 3.4 wpo-service-section */
.wpo-service-section,
.wpo-service-section-s2,
.wpo-service-section-s3 {
  padding-bottom: 70px;
}

.wpo-service-section .wpo-section-title-s2,
.wpo-service-section-s2 .wpo-section-title-s2,
.wpo-service-section-s3 .wpo-section-title-s2 {
  max-width: 512px;
}

@media (max-width: 991px) {
  .wpo-service-section .wpo-section-title-s2,
  .wpo-service-section-s2 .wpo-section-title-s2,
  .wpo-service-section-s3 .wpo-section-title-s2 {
    margin: 0 auto;
    margin-bottom: 20px;
    text-align: center;
  }
}

.wpo-service-section .wpo-section-title-img,
.wpo-service-section-s2 .wpo-section-title-img,
.wpo-service-section-s3 .wpo-section-title-img {
  text-align: right;
  padding-right: 40px;
  padding-left: 40px;
  position: relative;
  max-width: 400px;
  margin-left: auto;
  z-index: 1;
  margin-bottom: 50px;
}

@media (max-width: 991px) {
  .wpo-service-section .wpo-section-title-img,
  .wpo-service-section-s2 .wpo-section-title-img,
  .wpo-service-section-s3 .wpo-section-title-img {
    margin: 0 auto;
    margin-bottom: 50px;
  }
}

.wpo-service-section .wpo-section-title-img:before,
.wpo-service-section-s2 .wpo-section-title-img:before,
.wpo-service-section-s3 .wpo-section-title-img:before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 110px;
  content: "";
  background: #F5F6E4;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: -1;
}

.wpo-service-section .wpo-section-title-img:after,
.wpo-service-section-s2 .wpo-section-title-img:after,
.wpo-service-section-s3 .wpo-section-title-img:after {
  position: absolute;
  top: 50%;
  right: 0;
  width: 40px;
  height: 110px;
  content: "";
  background: #E5F346;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.wpo-service-section .wpo-service-wrap .wpo-service-item,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 50px;
}

@media (max-width: 767px) {
  .wpo-service-section .wpo-service-wrap .wpo-service-item,
  .wpo-service-section-s2 .wpo-service-wrap .wpo-service-item,
  .wpo-service-section-s3 .wpo-service-wrap .wpo-service-item {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    text-align: center;
  }
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-icon,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-icon,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-icon {
  margin-right: 25px;
}

@media (max-width: 767px) {
  .wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-icon,
  .wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-icon,
  .wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-icon {
    margin-right: 0;
    margin-bottom: 20px;
  }
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-icon span,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-icon span,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-icon span {
  width: 100px;
  height: 100px;
  line-height: 100px;
  border: 1px dashed rgba(28, 24, 23, 0.12);
  display: block;
  text-align: center;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-icon span:before,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-icon span:before,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-icon span:before {
  position: absolute;
  left: 8px;
  top: 8px;
  width: 82px;
  height: 82px;
  background: #F5F6E4;
  content: "";
  border-radius: 50%;
  z-index: -1;
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-text h3,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-text h3,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 {
  font-size: 19px;
  font-style: normal;
  font-weight: 600;
  line-height: 33px;
  text-transform: capitalize;
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a {
  color: #1C1817;
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a:hover,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a:hover,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 a:hover {
  color: #adb927;
}

.wpo-service-section .wpo-service-wrap .wpo-service-item .wpo-service-text p,
.wpo-service-section-s2 .wpo-service-wrap .wpo-service-item .wpo-service-text p,
.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-text p {
  margin-bottom: 0;
}

/* 3.5 wpo-plan-section */
.wpo-plan-section,
.wpo-plan-section-s2 {
  background: #F5F6E4;
  position: relative;
  z-index: 1;
}

.wpo-plan-section:before,
.wpo-plan-section-s2:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-plan-section .wpo-section-title,
.wpo-plan-section-s2 .wpo-section-title {
  margin-bottom: 35px;
}

.wpo-plan-section .wpo-section-title small,
.wpo-plan-section-s2 .wpo-section-title small {
  background: #fff;
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs {
  border-bottom: 0;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 80px;
}

@media (max-width: 991px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs {
    margin-bottom: 30px;
  }
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
  width: 210px;
  height: 60px;
  line-height: 50px;
  border: 1px solid #1C1817;
  text-align: center;
  margin: 0 10px;
  border-radius: 0;
  font-family: "Epilogue";
  font-size: 17px;
  font-style: normal;
  font-weight: 500;
  text-transform: uppercase;
  color: #1C1817;
}

@media (max-width: 1399px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
    width: 150px;
    height: 50px;
    line-height: 40px;
    font-size: 15px;
  }
}

@media (max-width: 991px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
    width: 125px;
    height: 40px;
    line-height: 25px;
    font-size: 13px;
    margin: 0 5px;
    padding: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
    margin: 0 5px 8px;
  }
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link.active,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link.active {
  background: #E5F346;
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content-box,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content-box {
  padding-left: 30px;
}

@media (max-width: 991px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-content-box,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content-box {
    padding-left: 0;
    padding-top: 40px;
  }
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content {
  padding: 30px;
  background: #E5F346;
  border: 1px solid #1C1817;
  position: relative;
  z-index: 1;
}

@media (max-width: 1399px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-content,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content {
    padding: 20px;
  }
}

@media (max-width: 991px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-content,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content {
    padding: 20px;
  }
}

@media (max-width: 991px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-content img,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content img {
    width: 100%;
  }
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content:before,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content ul,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content ul {
  list-style: none;
  margin-top: 55px;
}

@media (max-width: 1399px) {
  .wpo-plan-section .wpo-plan-wrap .wpo-plan-content ul,
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content ul {
    margin-top: 40px;
  }
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content ul li,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content ul li {
  font-family: "Epilogue";
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 26px;
  color: #1C1817;
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content ul li span,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content ul li span {
  font-size: 28px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
}

.wpo-plan-section .wpo-plan-wrap .wpo-plan-content a,
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content a {
  display: block;
  padding: 15px 25px;
  background: #1C1817;
  text-align: center;
  color: #fff;
  text-transform: capitalize;
}

/* 3.6 wpo-property-section */
.wpo-property-section .wpo-section-title,
.wpo-property-section-s2 .wpo-section-title {
  text-align: left;
}

@media (max-width: 1199px) {
  .wpo-property-section .wpo-section-title h2,
  .wpo-property-section-s2 .wpo-section-title h2 {
    font-size: 45px;
  }
}

@media (max-width: 767px) {
  .wpo-property-section .wpo-section-title h2,
  .wpo-property-section-s2 .wpo-section-title h2 {
    font-size: 32px;
    line-height: 40px;
  }
}

@media (max-width: 330px) {
  .wpo-property-section .wpo-section-title h2,
  .wpo-property-section-s2 .wpo-section-title h2 {
    font-size: 30px;
  }
}

.wpo-property-section .card-single .image,
.wpo-property-section-s2 .card-single .image {
  position: relative;
  overflow: hidden;
}

.wpo-property-section .card-single .image::before,
.wpo-property-section-s2 .card-single .image::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: '';
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(75, 56, 50, 0)), color-stop(78.87%, rgba(75, 56, 50, 0.95)));
  background: linear-gradient(180deg, rgba(75, 56, 50, 0) 0%, rgba(75, 56, 50, 0.95) 78.87%);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  z-index: 1;
}

.wpo-property-section .card-single .image img,
.wpo-property-section-s2 .card-single .image img {
  width: 100%;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.wpo-property-section .card-single .image ul,
.wpo-property-section-s2 .card-single .image ul {
  position: absolute;
  left: 0;
  bottom: -10px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  z-index: 11;
}

.wpo-property-section .card-single .image ul li,
.wpo-property-section-s2 .card-single .image ul li {
  display: inline-block;
  background: #F7F2EE;
  padding: 30px 20px;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  list-style: none;
}

@media (max-width: 1399px) {
  .wpo-property-section .card-single .image ul li,
  .wpo-property-section-s2 .card-single .image ul li {
    padding: 20px 17px;
  }
}

@media (max-width: 991px) {
  .wpo-property-section .card-single .image ul li,
  .wpo-property-section-s2 .card-single .image ul li {
    padding: 18px 10px;
  }
}

@media (max-width: 375px) {
  .wpo-property-section .card-single .image ul li,
  .wpo-property-section-s2 .card-single .image ul li {
    padding: 10px 5px;
  }
}

.wpo-property-section .card-single .image ul li .icon,
.wpo-property-section-s2 .card-single .image ul li .icon {
  width: 27px;
  height: 27px;
  margin: 0 auto;
}

.wpo-property-section .card-single .image ul li .text-dos,
.wpo-property-section-s2 .card-single .image ul li .text-dos {
  color: #524946;
  text-align: center;
  font-size: 13px;
  font-family: Manrope;
  font-weight: 400;
  text-transform: capitalize;
  margin-top: 15px;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.wpo-property-section .card-single .image ul li:hover,
.wpo-property-section-s2 .card-single .image ul li:hover {
  background: #fff;
}

.wpo-property-section .card-single .text,
.wpo-property-section-s2 .card-single .text {
  margin-top: 30px;
  margin-left: 15px;
}

.wpo-property-section .card-single .text h2,
.wpo-property-section-s2 .card-single .text h2 {
  margin-bottom: 0;
}

.wpo-property-section .card-single .text h2 a,
.wpo-property-section-s2 .card-single .text h2 a {
  color: #4B3832;
  font-size: 22px;
  font-family: "Epilogue";
  font-weight: 400;
  line-height: 33px;
  text-transform: uppercase;
}

.wpo-property-section .card-single .text span,
.wpo-property-section-s2 .card-single .text span {
  color: #9A9695;
  font-size: 16px;
  font-weight: 400;
  line-height: 32px;
}

.wpo-property-section .card-single:hover .image::before,
.wpo-property-section-s2 .card-single:hover .image::before {
  opacity: 1;
  visibility: visible;
}

.wpo-property-section .card-single:hover .image img,
.wpo-property-section-s2 .card-single:hover .image img {
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
}

.wpo-property-section .card-single:hover .image ul,
.wpo-property-section-s2 .card-single:hover .image ul {
  opacity: 1;
  visibility: visible;
  bottom: 0;
}

.wpo-property-section .owl-nav [class*=owl-],
.wpo-property-section-s2 .owl-nav [class*=owl-] {
  background: transparent;
  width: 58px;
  height: 58px;
  line-height: 63px;
  padding: 0;
  margin: 0;
  border-radius: 0px;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: #F5F6E4;
  border: 1px solid transparent;
  margin-right: 10px;
  top: -150px;
  position: absolute;
  left: auto;
  right: 75px;
}

@media (max-width: 767px) {
  .wpo-property-section .owl-nav [class*=owl-],
  .wpo-property-section-s2 .owl-nav [class*=owl-] {
    display: none;
  }
}

.wpo-property-section .owl-nav [class*=owl-]:hover,
.wpo-property-section-s2 .owl-nav [class*=owl-]:hover {
  background: #1C1817;
  border: 1px solid transparent;
  color: #fff;
}

.wpo-property-section .owl-nav [class*=owl-].owl-next,
.wpo-property-section-s2 .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: 0;
}

.wpo-property-section .owl-nav [class*=owl-] .fi::before,
.wpo-property-section-s2 .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

.wpo-property-section .owl-dots,
.wpo-property-section-s2 .owl-dots {
  position: absolute;
  right: 0;
  top: -145px;
  display: none;
}

@media (max-width: 991px) {
  .wpo-property-section .owl-dots,
  .wpo-property-section-s2 .owl-dots {
    position: unset;
    text-align: center;
    margin-top: 25px;
    display: block;
  }
}

.wpo-property-section .owl-dots button.owl-dot,
.wpo-property-section-s2 .owl-dots button.owl-dot {
  width: 8px;
  height: 8px;
  border-radius: 100px;
  display: inline-block;
  background: #E5F346;
  margin: 0 9px;
  position: relative;
  border: transparent;
  padding: 0;
}

.wpo-property-section .owl-dots button.owl-dot::before,
.wpo-property-section-s2 .owl-dots button.owl-dot::before {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 20px;
  height: 20px;
  content: "";
  border: 1px solid transparent;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.wpo-property-section .owl-dots button.owl-dot.active,
.wpo-property-section-s2 .owl-dots button.owl-dot.active {
  background-color: #adb927;
}

.wpo-property-section .owl-dots button.owl-dot.active::before,
.wpo-property-section-s2 .owl-dots button.owl-dot.active::before {
  border: 1px solid #adb927;
}

.wpo-property-section .owl-dots button.owl-dot:focus,
.wpo-property-section-s2 .owl-dots button.owl-dot:focus {
  outline: none;
}

/* 3.7 wpo-place-section */
.wpo-place-section .container-fluid,
.wpo-place-section-s2 .container-fluid {
  padding-right: 0;
}

@media (max-width: 991px) {
  .wpo-place-section .container-fluid,
  .wpo-place-section-s2 .container-fluid {
    padding-right: 15px;
  }
}

.wpo-place-section .wpo-section-title-s2,
.wpo-place-section-s2 .wpo-section-title-s2 {
  padding-left: 300px;
}

@media (max-width: 1700px) {
  .wpo-place-section .wpo-section-title-s2,
  .wpo-place-section-s2 .wpo-section-title-s2 {
    padding-left: 200px;
  }
}

@media (max-width: 1500px) {
  .wpo-place-section .wpo-section-title-s2,
  .wpo-place-section-s2 .wpo-section-title-s2 {
    padding-left: 150px;
  }
}

@media (max-width: 1399px) {
  .wpo-place-section .wpo-section-title-s2,
  .wpo-place-section-s2 .wpo-section-title-s2 {
    padding-left: 100px;
  }
}

@media (max-width: 1199px) {
  .wpo-place-section .wpo-section-title-s2,
  .wpo-place-section-s2 .wpo-section-title-s2 {
    padding-left: 0px;
  }
}

@media (max-width: 767px) {
  .wpo-place-section .wpo-section-title-s2,
  .wpo-place-section-s2 .wpo-section-title-s2 {
    text-align: center;
  }
}

.wpo-place-section .wpo-section-title-s2 h2,
.wpo-place-section-s2 .wpo-section-title-s2 h2 {
  max-width: 420px;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  .wpo-place-section .wpo-section-title-s2 h2,
  .wpo-place-section-s2 .wpo-section-title-s2 h2 {
    max-width: 100%;
  }
}

.wpo-place-section .tabs,
.wpo-place-section-s2 .tabs {
  max-width: 400px;
  list-style: none;
}

@media (max-width: 767px) {
  .wpo-place-section .tabs,
  .wpo-place-section-s2 .tabs {
    max-width: 100%;
  }
}

.wpo-place-section .tabs li,
.wpo-place-section-s2 .tabs li {
  margin-bottom: 25px;
}

.wpo-place-section .tabs li button,
.wpo-place-section-s2 .tabs li button {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  color: #1C1817;
  font-family: "Epilogue";
  background: #F5F6E4;
  display: block;
  height: 68px;
  line-height: 68px;
  padding-left: 30px;
  position: relative;
  width: 100%;
  text-align: left;
  border: 0;
}

.wpo-place-section .tabs li button span,
.wpo-place-section-s2 .tabs li button span {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: #424740;
}

.wpo-place-section .tabs li button:before,
.wpo-place-section-s2 .tabs li button:before {
  position: absolute;
  right: 30px;
  top: 0;
  content: "\e628";
  font-family: "themify";
  line-height: unset;
}

.wpo-place-section .tabs li button.active,
.wpo-place-section-s2 .tabs li button.active {
  background: #1C1817;
  color: #fff;
}

.wpo-place-section .tabs li button.active span,
.wpo-place-section-s2 .tabs li button.active span {
  color: #fff;
}

.wpo-place-section .place-right-wrap,
.wpo-place-section-s2 .place-right-wrap {
  position: relative;
}

.wpo-place-section .place-right-wrap img,
.wpo-place-section-s2 .place-right-wrap img {
  width: 100%;
}

.wpo-place-section .place-right-wrap .location,
.wpo-place-section-s2 .place-right-wrap .location {
  position: absolute;
  left: 15%;
  top: 20%;
}

@media (max-width: 575px) {
  .wpo-place-section .place-right-wrap .location img,
  .wpo-place-section-s2 .place-right-wrap .location img {
    max-width: 30px;
  }
}

.wpo-place-section .place-right-wrap .location.s2,
.wpo-place-section-s2 .place-right-wrap .location.s2 {
  left: 50%;
  top: 20%;
}

.wpo-place-section .place-right-wrap .location.s3,
.wpo-place-section-s2 .place-right-wrap .location.s3 {
  left: 62%;
  top: 40%;
}

.wpo-place-section .place-right-wrap .location.s4,
.wpo-place-section-s2 .place-right-wrap .location.s4 {
  left: 82%;
  top: 35%;
}

.wpo-place-section .place-right-wrap .location.s5,
.wpo-place-section-s2 .place-right-wrap .location.s5 {
  left: 30%;
  top: 60%;
}

.wpo-place-section .place-right-wrap .location.s6,
.wpo-place-section-s2 .place-right-wrap .location.s6 {
  left: 74%;
  top: 67%;
}

.wpo-place-section .place-right-wrap .location .pin,
.wpo-place-section-s2 .place-right-wrap .location .pin {
  position: relative;
  z-index: 1;
}

.wpo-place-section .place-right-wrap .location .pin span,
.wpo-place-section-s2 .place-right-wrap .location .pin span {
  position: absolute;
  left: -65px;
  bottom: -50px;
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  text-transform: capitalize;
  background: #E5F346;
  width: 175px;
  text-align: center;
  border-radius: 30px;
  opacity: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: all .3s;
  transition: all .3s;
  z-index: 11;
}

@media (max-width: 575px) {
  .wpo-place-section .place-right-wrap .location .pin span,
  .wpo-place-section-s2 .place-right-wrap .location .pin span {
    width: 125px;
    font-size: 10px;
    left: -48px;
    bottom: -45px;
  }
}

.wpo-place-section .place-right-wrap .location .pin span:before,
.wpo-place-section-s2 .place-right-wrap .location .pin span:before {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  top: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #E5F346;
  content: "";
}

.wpo-place-section .place-right-wrap .location .pin:hover span,
.wpo-place-section-s2 .place-right-wrap .location .pin:hover span {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
}

.wpo-place-section .place-right-wrap .pin1 .s1 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin1 .s1 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

.wpo-place-section .place-right-wrap .pin2 .s2 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin2 .s2 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

.wpo-place-section .place-right-wrap .pin3 .s3 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin3 .s3 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

.wpo-place-section .place-right-wrap .pin4 .s4 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin4 .s4 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

.wpo-place-section .place-right-wrap .pin5 .s5 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin5 .s5 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

.wpo-place-section .place-right-wrap .pin6 .s6 .pin span,
.wpo-place-section-s2 .place-right-wrap .pin6 .s6 .pin span {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}

/* 3.8 wpo-team-section */
.wpo-team-section,
.wpo-team-section-s2 {
  background: #F5F6E4;
  position: relative;
  z-index: 1;
}

.wpo-team-section:before,
.wpo-team-section-s2:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-team-section:after,
.wpo-team-section-s2:after {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/team/noice.png);
  z-index: -1;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item {
  background: #fff;
  text-align: center;
  padding: 60px 35px;
}

@media (max-width: 1399px) {
  .wpo-team-section .wpo-team-wrap .wpo-team-item,
  .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item,
  .wpo-team-section .wpo-team-wrap-s3 .wpo-team-item,
  .wpo-team-section-s2 .wpo-team-wrap .wpo-team-item,
  .wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item,
  .wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item {
    padding: 30px 15px;
  }
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-img,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-img,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-img,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-img,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-img {
  max-width: 154px;
  margin: 0 auto;
  padding: 13px;
  border-radius: 50%;
  border: 1px dashed #837d7c;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img img,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-img img,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-img img,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-img img,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-img img,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-img img {
  border-radius: 50%;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content {
  margin-top: 32px;
}

@media (max-width: 1399px) {
  .wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content,
  .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content,
  .wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content,
  .wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content,
  .wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content,
  .wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content {
    margin-top: 20px;
  }
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content p,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content p,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content p,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content p,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content p,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content p {
  font-family: "Epilogue";
  font-size: 23px;
  font-style: normal;
  font-weight: 600;
  line-height: 42px;
  text-transform: uppercase;
  color: #1C1817;
  margin-bottom: 0;
}

@media (max-width: 1399px) {
  .wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content p,
  .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content p,
  .wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content p,
  .wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content p,
  .wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content p,
  .wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content p {
    font-size: 18px;
  }
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content span,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content span,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content span,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content span,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content span,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content span {
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  text-transform: capitalize;
  color: #424740;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content h3,
.wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content h3,
.wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content h3,
.wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content h3,
.wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content h3,
.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content h3 {
  font-size: 35px;
  font-style: normal;
  font-weight: 500;
  line-height: 45px;
  text-transform: uppercase;
  margin-top: 20px;
}

@media (max-width: 1399px) {
  .wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-content h3,
  .wpo-team-section .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content h3,
  .wpo-team-section .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content h3,
  .wpo-team-section-s2 .wpo-team-wrap .wpo-team-item .wpo-team-content h3,
  .wpo-team-section-s2 .wpo-team-wrap-s2 .wpo-team-item .wpo-team-content h3,
  .wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item .wpo-team-content h3 {
    font-size: 30px;
    margin-bottom: 0;
  }
}

.wpo-team-section .wpo-team-wrap .owl-item.active.center .wpo-team-item,
.wpo-team-section .wpo-team-wrap-s2 .owl-item.active.center .wpo-team-item,
.wpo-team-section .wpo-team-wrap-s3 .owl-item.active.center .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap .owl-item.active.center .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-item.active.center .wpo-team-item,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-item.active.center .wpo-team-item {
  background: #E5F346;
}

.wpo-team-section .wpo-team-wrap .owl-dots,
.wpo-team-section .wpo-team-wrap-s2 .owl-dots,
.wpo-team-section .wpo-team-wrap-s3 .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-dots {
  display: none;
}

@media (max-width: 1199px) {
  .wpo-team-section .wpo-team-wrap .owl-dots,
  .wpo-team-section .wpo-team-wrap-s2 .owl-dots,
  .wpo-team-section .wpo-team-wrap-s3 .owl-dots,
  .wpo-team-section-s2 .wpo-team-wrap .owl-dots,
  .wpo-team-section-s2 .wpo-team-wrap-s2 .owl-dots,
  .wpo-team-section-s2 .wpo-team-wrap-s3 .owl-dots {
    display: block;
  }
}

.wpo-team-section .wpo-team-wrap .owl-dots,
.wpo-team-section .wpo-team-wrap-s2 .owl-dots,
.wpo-team-section .wpo-team-wrap-s3 .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-dots,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-dots {
  text-align: center;
  margin-top: 40px;
}

.wpo-team-section .wpo-team-wrap .owl-dot,
.wpo-team-section .wpo-team-wrap-s2 .owl-dot,
.wpo-team-section .wpo-team-wrap-s3 .owl-dot,
.wpo-team-section-s2 .wpo-team-wrap .owl-dot,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-dot,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-dot {
  width: 10px;
  height: 12px;
  background: #E5F346;
  border-radius: 50%;
  margin: 0 5px;
  border: 0;
}

.wpo-team-section .wpo-team-wrap .owl-dot.active,
.wpo-team-section .wpo-team-wrap-s2 .owl-dot.active,
.wpo-team-section .wpo-team-wrap-s3 .owl-dot.active,
.wpo-team-section-s2 .wpo-team-wrap .owl-dot.active,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-dot.active,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-dot.active {
  background: #adb927;
}

.wpo-team-section .wpo-team-wrap .owl-nav [class*=owl-],
.wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-],
.wpo-team-section .wpo-team-wrap-s3 .owl-nav [class*=owl-],
.wpo-team-section-s2 .wpo-team-wrap .owl-nav [class*=owl-],
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-nav [class*=owl-],
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-nav [class*=owl-] {
  background: transparent;
  width: 50px;
  height: 50px;
  line-height: 56px;
  padding: 0;
  margin: 0;
  border-radius: 5px;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: #fff;
  border: 1px solid transparent;
  margin-right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  left: 30%;
}

@media (max-width: 1199px) {
  .wpo-team-section .wpo-team-wrap .owl-nav [class*=owl-],
  .wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-],
  .wpo-team-section .wpo-team-wrap-s3 .owl-nav [class*=owl-],
  .wpo-team-section-s2 .wpo-team-wrap .owl-nav [class*=owl-],
  .wpo-team-section-s2 .wpo-team-wrap-s2 .owl-nav [class*=owl-],
  .wpo-team-section-s2 .wpo-team-wrap-s3 .owl-nav [class*=owl-] {
    display: none;
  }
}

.wpo-team-section .wpo-team-wrap .owl-nav [class*=owl-]:hover,
.wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-]:hover,
.wpo-team-section .wpo-team-wrap-s3 .owl-nav [class*=owl-]:hover,
.wpo-team-section-s2 .wpo-team-wrap .owl-nav [class*=owl-]:hover,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-nav [class*=owl-]:hover,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-nav [class*=owl-]:hover {
  background: #E5F346;
  border: 1px solid transparent;
}

.wpo-team-section .wpo-team-wrap .owl-nav [class*=owl-].owl-next,
.wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-].owl-next,
.wpo-team-section .wpo-team-wrap-s3 .owl-nav [class*=owl-].owl-next,
.wpo-team-section-s2 .wpo-team-wrap .owl-nav [class*=owl-].owl-next,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-nav [class*=owl-].owl-next,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: 29%;
}

.wpo-team-section .wpo-team-wrap .owl-nav [class*=owl-] .fi::before,
.wpo-team-section .wpo-team-wrap-s2 .owl-nav [class*=owl-] .fi::before,
.wpo-team-section .wpo-team-wrap-s3 .owl-nav [class*=owl-] .fi::before,
.wpo-team-section-s2 .wpo-team-wrap .owl-nav [class*=owl-] .fi::before,
.wpo-team-section-s2 .wpo-team-wrap-s2 .owl-nav [class*=owl-] .fi::before,
.wpo-team-section-s2 .wpo-team-wrap-s3 .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

/* 3.9 wpo-testimonial-section */
.wpo-testimonial-section,
.wpo-testimonial-section-s2 {
  border-bottom: 1px solid #EAEBDD;
}

.wpo-testimonial-section .wpo-section-title-tp,
.wpo-testimonial-section-s2 .wpo-section-title-tp {
  margin-bottom: 50px;
}

.wpo-testimonial-section .wpo-section-title-s2,
.wpo-testimonial-section-s2 .wpo-section-title-s2 {
  margin-bottom: 0;
  max-width: 546px;
}

.wpo-testimonial-section .wpo-section-title-s2 h2,
.wpo-testimonial-section-s2 .wpo-section-title-s2 h2 {
  margin-bottom: 0;
}

@media (max-width: 767px) {
  .wpo-testimonial-section .wpo-section-title-s2,
  .wpo-testimonial-section-s2 .wpo-section-title-s2 {
    text-align: center;
  }
}

@media (max-width: 991px) {
  .wpo-testimonial-section .wpo-section-title-img,
  .wpo-testimonial-section-s2 .wpo-section-title-img {
    display: none;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .item,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .item {
  background: #F5F6E4;
  padding: 40px;
}

@media (max-width: 575px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .item,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .item {
    padding: 15px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-top-wrap,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-top-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 30px;
}

@media (max-width: 575px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .testimonial-top-wrap,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-top-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-top-wrap ul,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-top-wrap ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  list-style: none;
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-top-wrap ul li,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-top-wrap ul li {
  color: #94a100;
  font-size: 18px;
}

@media (max-width: 575px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .testimonial-top-wrap,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-top-wrap {
    margin-bottom: 15px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-left-item,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-left-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 575px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .testimonial-left-item,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-left-item {
    margin-bottom: 15px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-left-item .image,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-left-item .image {
  max-width: 92px;
  margin-right: 20px;
  position: relative;
  padding-left: 10px;
  padding-top: 10px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-left-item .image::before,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-left-item .image::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 60px;
  height: 60px;
  content: "";
  background: #fff;
}

.wpo-testimonial-section .wpo-testimonial-wrap .testimonial-text p,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-text p {
  font-size: 19px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  color: #424740;
  margin-bottom: 0;
}

@media (max-width: 575px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .testimonial-text p,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .testimonial-text p {
    font-size: 16px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dots,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots {
  display: none;
}

@media (max-width: 767px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-dots,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots {
    display: block;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dots,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots {
  text-align: center;
  margin-top: 40px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dot,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dot {
  width: 10px;
  height: 12px;
  background: #E5F346;
  border-radius: 50%;
  margin: 0 5px;
  border: 0;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dot.active,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dot.active {
  background: #adb927;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-],
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] {
  background: transparent;
  width: 58px;
  height: 58px;
  line-height: 63px;
  padding: 0;
  margin: 0;
  border-radius: 0px;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: #F5F6E4;
  border: 1px solid transparent;
  margin-right: 10px;
  top: -150px;
  position: absolute;
  left: auto;
  right: 75px;
}

@media (max-width: 767px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-],
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] {
    display: none;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover {
  background: #1C1817;
  border: 1px solid transparent;
  color: #fff;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-].owl-next,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: 0;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-] .fi::before,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

/* 3.10 wpo-blog-section */
.wpo-blog-section,
.wpo-blog-section-s2 {
  padding-right: 85px;
}

@media (max-width: 1399px) {
  .wpo-blog-section,
  .wpo-blog-section-s2 {
    padding-right: 0;
  }
}

.wpo-blog-section .wpo-section-title-s2,
.wpo-blog-section-s2 .wpo-section-title-s2 {
  padding-left: 300px;
}

@media (max-width: 1700px) {
  .wpo-blog-section .wpo-section-title-s2,
  .wpo-blog-section-s2 .wpo-section-title-s2 {
    padding-left: 200px;
  }
}

@media (max-width: 1500px) {
  .wpo-blog-section .wpo-section-title-s2,
  .wpo-blog-section-s2 .wpo-section-title-s2 {
    padding-left: 150px;
  }
}

@media (max-width: 1399px) {
  .wpo-blog-section .wpo-section-title-s2,
  .wpo-blog-section-s2 .wpo-section-title-s2 {
    padding-left: 100px;
  }
}

@media (max-width: 1199px) {
  .wpo-blog-section .wpo-section-title-s2,
  .wpo-blog-section-s2 .wpo-section-title-s2 {
    padding-left: 0px;
  }
}

@media (max-width: 991px) {
  .wpo-blog-section .wpo-section-title-s2,
  .wpo-blog-section-s2 .wpo-section-title-s2 {
    text-align: center;
  }
}

.wpo-blog-section .wpo-section-title-s2 .theme-btn, .wpo-blog-section .wpo-section-title-s2 .view-cart-btn,
.wpo-blog-section-s2 .wpo-section-title-s2 .theme-btn,
.wpo-blog-section-s2 .wpo-section-title-s2 .view-cart-btn {
  background: #1C1817;
  color: #fff;
  text-transform: capitalize;
  margin-top: 30px;
}

@media (max-width: 991px) {
  .wpo-blog-section .wpo-section-title-s2 .theme-btn, .wpo-blog-section .wpo-section-title-s2 .view-cart-btn,
  .wpo-blog-section-s2 .wpo-section-title-s2 .theme-btn,
  .wpo-blog-section-s2 .wpo-section-title-s2 .view-cart-btn {
    margin-top: 0px;
  }
}

.wpo-blog-section .wpo-section-title-s2 .theme-btn:hover, .wpo-blog-section .wpo-section-title-s2 .view-cart-btn:hover,
.wpo-blog-section-s2 .wpo-section-title-s2 .theme-btn:hover,
.wpo-blog-section-s2 .wpo-section-title-s2 .view-cart-btn:hover {
  background: #E5F346;
  color: #1C1817;
}

.wpo-blog-section .blog-item,
.wpo-blog-section-s2 .blog-item {
  position: relative;
}

@media (max-width: 991px) {
  .wpo-blog-section .blog-item,
  .wpo-blog-section-s2 .blog-item {
    margin-bottom: 30px;
  }
}

.wpo-blog-section .blog-item .image,
.wpo-blog-section-s2 .blog-item .image {
  position: relative;
}

.wpo-blog-section .blog-item .image img,
.wpo-blog-section-s2 .blog-item .image img {
  width: 100%;
}

.wpo-blog-section .blog-item .image .thumb,
.wpo-blog-section-s2 .blog-item .image .thumb {
  position: absolute;
  left: 0;
  bottom: 0;
}

.wpo-blog-section .blog-item .image .thumb span,
.wpo-blog-section-s2 .blog-item .image .thumb span {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
  padding: 10px 20px;
  display: inline-block;
  background: #E5F346;
}

.wpo-blog-section .blog-item .blog-content,
.wpo-blog-section-s2 .blog-item .blog-content {
  padding-top: 30px;
}

.wpo-blog-section .blog-item .blog-content h2,
.wpo-blog-section-s2 .blog-item .blog-content h2 {
  font-size: 23px;
  font-style: normal;
  font-weight: 600;
  line-height: 38px;
  text-transform: uppercase;
  max-width: 360px;
  margin-bottom: 25px;
}

.wpo-blog-section .blog-item .blog-content h2 a,
.wpo-blog-section-s2 .blog-item .blog-content h2 a {
  color: #1C1817;
}

.wpo-blog-section .blog-item .blog-content h2 a:hover,
.wpo-blog-section-s2 .blog-item .blog-content h2 a:hover {
  color: #adb927;
}

.wpo-blog-section .blog-item .blog-content .read-more,
.wpo-blog-section-s2 .blog-item .blog-content .read-more {
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  text-transform: capitalize;
  color: #1C1817;
  padding: 10px 20px;
  border: 1px solid #1C1817;
}

.wpo-blog-section .blog-item .blog-content .read-more:hover,
.wpo-blog-section-s2 .blog-item .blog-content .read-more:hover {
  background: #E5F346;
}

/*--------------------------------------------------------------
5. Home-style-2
--------------------------------------------------------------*/
/* 5.1 features-content */
@media (max-width: 991px) {
  .features-content,
  .features-content-s2 {
    padding-top: 90px;
  }
}

@media (max-width: 767px) {
  .features-content,
  .features-content-s2 {
    padding-top: 80px;
  }
}

.features-content .features-content-inner,
.features-content-s2 .features-content-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: #fff;
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  max-width: 1243px;
  padding: 0 60px;
  padding-right: 0;
  margin-top: -109px;
  z-index: 11;
  position: relative;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 991px) {
  .features-content .features-content-inner,
  .features-content-s2 .features-content-inner {
    margin-top: 0px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    padding: 0 20px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.features-content .features-content-inner .features-content-item,
.features-content-s2 .features-content-inner .features-content-item {
  padding: 40px 0;
}

@media (max-width: 991px) {
  .features-content .features-content-inner .features-content-item,
  .features-content-s2 .features-content-inner .features-content-item {
    -ms-flex-preferred-size: 33.33%;
        flex-basis: 33.33%;
    text-align: center;
  }
}

@media (max-width: 450px) {
  .features-content .features-content-inner .features-content-item,
  .features-content-s2 .features-content-inner .features-content-item {
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
  }
}

.features-content .features-content-inner .features-content-item h2,
.features-content-s2 .features-content-inner .features-content-item h2 {
  font-size: 42px;
  font-style: normal;
  font-weight: 600;
}

@media (max-width: 991px) {
  .features-content .features-content-inner .features-content-item h2,
  .features-content-s2 .features-content-inner .features-content-item h2 {
    font-size: 25px;
  }
}

.features-content .features-content-inner .features-content-item span,
.features-content-s2 .features-content-inner .features-content-item span {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
}

@media (max-width: 991px) {
  .features-content .features-content-inner .features-content-item span,
  .features-content-s2 .features-content-inner .features-content-item span {
    font-size: 13px;
  }
}

.features-content .features-content-inner .features-content-item .theme-btn, .features-content .features-content-inner .features-content-item .view-cart-btn,
.features-content-s2 .features-content-inner .features-content-item .theme-btn,
.features-content-s2 .features-content-inner .features-content-item .view-cart-btn {
  margin-right: -50px;
}

@media (max-width: 991px) {
  .features-content .features-content-inner .features-content-item .theme-btn, .features-content .features-content-inner .features-content-item .view-cart-btn,
  .features-content-s2 .features-content-inner .features-content-item .theme-btn,
  .features-content-s2 .features-content-inner .features-content-item .view-cart-btn {
    margin-right: 0;
  }
}

/* 5.2 wpo-service-section-s2 */
.wpo-service-section-s2 .left-img {
  background: url(../images/service/shape.jpg) no-repeat center center;
  background-size: cover;
  max-width: 100%;
  margin-right: 60px;
}

@media (max-width: 1399px) {
  .wpo-service-section-s2 .left-img {
    margin-right: 0px;
  }
}

@media (max-width: 991px) {
  .wpo-service-section-s2 .left-img {
    margin-bottom: 30px;
  }
}

.wpo-service-section-s2 .left-img img {
  mix-blend-mode: screen;
  width: 100%;
}

.wpo-service-section-s2 .wpo-service-wrapper .wpo-section-title-img::before, .wpo-service-section-s2 .wpo-service-wrapper .wpo-section-title-img::after {
  display: none;
}

.wpo-service-section-s2 .wpo-service-wrapper .wpo-service-wrap {
  padding-left: 50px;
}

@media (max-width: 1399px) {
  .wpo-service-section-s2 .wpo-service-wrapper .wpo-service-wrap {
    padding-left: 0;
  }
}

/* 5.3 wpo-plan-section-s2 */
.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs {
  display: block;
  margin-bottom: 0;
}

.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
  margin: 0;
  width: 100%;
  margin-bottom: 23px;
}

@media (max-width: 991px) {
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-tab-menu ul.nav-tabs .nav-item .nav-link {
    height: 56px;
    line-height: 35px;
  }
}

.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-item {
  max-width: 311px;
  overflow: hidden;
  position: relative;
}

@media (max-width: 991px) {
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-item {
    max-width: 100%;
  }
}

.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-item:before {
  position: absolute;
  left: 12px;
  top: 92px;
  content: "";
  background: #E5F346;
  border: 1px solid #1C1817;
  width: 40px;
  height: 40px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

@media (max-width: 991px) {
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-item:before {
    display: none;
  }
}

@media (max-width: 991px) {
  .wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-item .wpo-plan-content-box {
    padding-top: 0;
  }
}

.wpo-plan-section-s2 .wpo-plan-wrap .tab-pane:nth-child(1) .wpo-plan-item:before {
  top: 7px;
}

.wpo-plan-section-s2 .wpo-plan-wrap .tab-pane:nth-child(3) .wpo-plan-item:before {
  top: 175px;
}

.wpo-plan-section-s2 .wpo-plan-wrap .tab-pane:nth-child(4) .wpo-plan-item:before {
  top: auto;
  bottom: 92px;
}

.wpo-plan-section-s2 .wpo-plan-wrap .tab-pane:nth-child(5) .wpo-plan-item:before {
  top: auto;
  bottom: 7px;
}

.wpo-plan-section-s2 .wpo-plan-wrap .wpo-plan-content ul {
  margin-top: 0;
}

.wpo-plan-section-s2 .wpo-plan-img {
  position: relative;
  width: 120%;
}

@media (max-width: 991px) {
  .wpo-plan-section-s2 .wpo-plan-img {
    width: 100%;
    margin-top: 30px;
  }
}

/* 5.4 wpo-place-section-s2 */
.wpo-place-section-s2 .wpo-section-title h2 {
  margin: 0 auto;
  max-width: 650px;
  margin-bottom: 30px;
}

.wpo-place-section-s2 .tabs {
  max-width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (max-width: 991px) {
  .wpo-place-section-s2 .tabs {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-place-section-s2 .tabs li {
  -ms-flex-preferred-size: 20%;
      flex-basis: 20%;
  margin: 0 10px;
}

@media (max-width: 991px) {
  .wpo-place-section-s2 .tabs li {
    margin: 0 5px;
    margin-bottom: 10px;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
}

@media (max-width: 1199px) {
  .wpo-place-section-s2 .tabs li button {
    padding-left: 10px;
    font-size: 14px;
    height: 55px;
    line-height: 58px;
  }
}

@media (max-width: 991px) {
  .wpo-place-section-s2 .tabs li button {
    padding: 0 15px;
  }
}

@media (max-width: 450px) {
  .wpo-place-section-s2 .tabs li button {
    padding: 0 10px;
    font-size: 12px;
  }
}

.wpo-place-section-s2 .tabs li button:before {
  display: none;
}

/* 5.5 wpo-payment-section-s2   */
.wpo-payment-section-s2 {
  background: none;
  border-bottom: 1px solid #EAEBDD;
}

.wpo-payment-section-s2:before {
  display: none;
}

.wpo-payment-section-s2::after {
  display: none;
}

.wpo-payment-section-s2 .wpo-payment-wrap {
  background: #E5F346;
  padding: 70px;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.wpo-payment-section-s2 .wpo-payment-wrap:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-payment-section-s2 .wpo-payment-wrap:after {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: url(../images/payment-bg.png), #E5F346 50%/cover no-repeat;
  z-index: -2;
  content: "";
  background-repeat: no-repeat;
  background-size: cover;
}

@media (max-width: 767px) {
  .wpo-payment-section-s2 .wpo-payment-wrap {
    padding: 40px;
  }
}

.wpo-payment-section-s2 .wpo-payment-wrap .payment-form-area .form-group:last-child {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
}

.wpo-payment-section-s2 .wpo-payment-wrap .payment-result {
  margin-top: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 50px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (max-width: 767px) {
  .wpo-payment-section-s2 .wpo-payment-wrap .payment-result {
    padding: 30px;
  }
}

@media (max-width: 575px) {
  .wpo-payment-section-s2 .wpo-payment-wrap .payment-result {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 20px 15px;
  }
}

.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item {
  margin-bottom: 0;
}

@media (max-width: 575px) {
  .wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item {
    margin: 0 20px;
    margin-bottom: 20px;
  }
}

.wpo-payment-section-s2 .wpo-payment-wrap .payment-result .result-item h3 {
  margin-bottom: 0;
}

/* 5.6 wpo-testimonial-section-s2  */
.wpo-testimonial-section-s2 {
  background: #F5F6E4;
  z-index: 1;
  position: relative;
}

.wpo-testimonial-section-s2:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .item {
  background: #fff;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] {
  background: #E5F346;
}

/* 5.7 wpo-faq-section  */
.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding-right: 75px;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

@media (max-width: 1399px) {
  .wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap {
    padding-right: 30px;
  }
}

@media (max-width: 1199px) {
  .wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap {
    padding-right: 0px;
  }
}

@media (max-width: 991px) {
  .wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap {
    padding-right: 0px;
    padding-bottom: 30px;
  }
}

.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap .wpo-faq-left-img {
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
}

.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap .wpo-faq-left-img img {
  width: 100%;
}

.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap .wpo-faq-left-img:nth-child(1) {
  padding-right: 15px;
  padding-bottom: 30px;
}

.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap .wpo-faq-left-img:nth-child(2) {
  padding-left: 15px;
  padding-bottom: 30px;
}

.wpo-faq-section .wpo-faq-wrapper .wpo-faq-left-img-wrap .wpo-faq-left-img:last-child {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
}

/* 5.8 wpo-blog-section-s2 */
.wpo-blog-section-s2 {
  padding-right: 0;
  border-top: 1px solid #EAEBDD;
}

.wpo-blog-section-s2 .wpo-section-title-s2 {
  padding-left: 0;
}

.wpo-blog-section-s2 .blog-slide-active .owl-stage-outer {
  padding-bottom: 5px;
}

.wpo-blog-section-s2 .blog-slide-active .owl-dots {
  display: none;
}

@media (max-width: 767px) {
  .wpo-blog-section-s2 .blog-slide-active .owl-dots {
    display: block;
  }
}

.wpo-blog-section-s2 .blog-slide-active .owl-dots {
  text-align: center;
  margin-top: 40px;
}

.wpo-blog-section-s2 .blog-slide-active .owl-dot {
  width: 10px;
  height: 12px;
  background: #E5F346;
  border-radius: 50%;
  margin: 0 5px;
  border: 0;
}

.wpo-blog-section-s2 .blog-slide-active .owl-dot.active {
  background: #adb927;
}

.wpo-blog-section-s2 .blog-slide-active .owl-nav [class*=owl-] {
  background: transparent;
  width: 58px;
  height: 58px;
  line-height: 63px;
  padding: 0;
  margin: 0;
  border-radius: 0px;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background: #F5F6E4;
  border: 1px solid transparent;
  margin-right: 10px;
  top: -150px;
  position: absolute;
  left: auto;
  right: 75px;
}

@media (max-width: 767px) {
  .wpo-blog-section-s2 .blog-slide-active .owl-nav [class*=owl-] {
    display: none;
  }
}

.wpo-blog-section-s2 .blog-slide-active .owl-nav [class*=owl-]:hover {
  background: #1C1817;
  border: 1px solid transparent;
  color: #fff;
}

.wpo-blog-section-s2 .blog-slide-active .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: 0;
}

.wpo-blog-section-s2 .blog-slide-active .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

/*--------------------------------------------------------------
5. Home-style-2
--------------------------------------------------------------*/
/*3.2 features-content-s2 */
.features-content-s2 {
  padding-top: 80px;
}

.features-content-s2 .features-content-inner {
  margin-top: 0;
  max-width: 100%;
  padding-right: 60px;
  border-radius: 200px;
}

@media (max-width: 991px) {
  .features-content-s2 .features-content-inner {
    padding: 40px 0;
    padding-right: 20px;
  }
}

@media (max-width: 575px) {
  .features-content-s2 .features-content-inner {
    border-radius: 150px;
  }
}


.features-content-s2 .features-content-inner .features-content-item .theme-btn,
.features-content-s2 .features-content-inner .features-content-item .view-cart-btn {
  margin-right: 0;
}

/* 3.2 wpo-about-area */
.wpo-about-area .wpo-about-img {
  position: relative;
}

.wpo-about-area .wpo-about-img:before {
  position: absolute;
  left: 3%;
  top: 3%;
  width: 94%;
  height: 94%;
  content: "";
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.wpo-about-area .wpo-about-text {
  padding-left: 80px;
}

@media (max-width: 1400px) {
  .wpo-about-area .wpo-about-text {
    padding-left: 40px;
  }
}

@media (max-width: 1200px) {
  .wpo-about-area .wpo-about-text {
    padding-left: 20px;
  }
}

@media (max-width: 991px) {
  .wpo-about-area .wpo-about-text {
    padding-left: 0px;
    padding-top: 30px;
  }
}

.wpo-about-area .wpo-about-text .wpo-about-title span {
  display: inline-block;
  font-size: 18px;
  line-height: 26px;
  color: #424740;
  padding-left: 80px;
  position: relative;
}

.wpo-about-area .wpo-about-text .wpo-about-title span:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 70px;
  height: 1px;
  content: "";
  background: #424740;
}

.wpo-about-area .wpo-about-text .wpo-about-title h2 {
  font-weight: 700;
  font-size: 45px;
  line-height: 65px;
  margin-top: 20px;
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .wpo-about-area .wpo-about-text .wpo-about-title h2 {
    font-size: 35px;
    line-height: 55px;
    margin-bottom: 20px;
  }
}

@media (max-width: 450px) {
  .wpo-about-area .wpo-about-text .wpo-about-title h2 {
    font-size: 25px;
    line-height: 40px;
  }
}

.wpo-about-area .wpo-about-text h5 {
  font-style: italic;
  font-weight: normal;
  font-size: 22px;
  line-height: 32px;
  color: #4D4C60;
  font-weight: 400;
  margin-bottom: 30px;
}

.wpo-about-area .wpo-about-text .btns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 40px;
}

.wpo-about-area .wpo-about-text .btns ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  list-style: none;
  padding-left: 20px;
}

.wpo-about-area .wpo-about-text .btns ul li .video-btn {
  display: block;
  width: 55px;
  height: 55px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 4px 2px rgba(146, 139, 104, 0.16);
          box-shadow: 0px 2px 4px 2px rgba(146, 139, 104, 0.16);
  text-align: center;
  line-height: 58px;
  border-radius: 50%;
  color: #E5F346;
  margin-right: 10px;
  position: relative;
}

.wpo-about-area .wpo-about-text .btns ul li .video-btn.btn:before {
  content: "";
  width: 0px;
  height: 0px;
  border-top: 9px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #adb927;
  position: absolute;
  left: 52%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@media (max-width: 450px) {
  .wpo-about-area .wpo-about-text .btns ul li.video-text {
    display: none;
  }
}

.wpo-about-area .wpo-about-text .btns ul li.video-text a {
  color: #424740;
  font-weight: 600;
  width: 100%;
  height: 100%;
  -webkit-box-shadow: none;
          box-shadow: none;
  background: none;
}

/* 3.2 wpo-service-section-s3 */
.wpo-service-section-s3 {
  background: #F5F6E4;
  position: relative;
  z-index: 1;
}

.wpo-service-section-s3:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item {
  display: block;
  text-align: center;
  padding: 60px 30px;
  background: #fff;
  margin-bottom: 25px;
}

@media (max-width: 1199px) {
  .wpo-service-section-s3 .wpo-service-wrap .wpo-service-item {
    padding: 30px 20px;
  }
}

.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-icon span {
  margin: 0 auto;
  margin-bottom: 20px;
}

.wpo-service-section-s3 .wpo-service-wrap .wpo-service-item .wpo-service-text h3 {
  font-size: 22px;
  margin-bottom: 10px;
}

/*===========================
6. About-Page
===========================*/
/*3.3 wpo-fun-fact-section */
.wpo-fun-fact-section,
.wpo-fun-fact-section-s2 {
  text-align: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.wpo-fun-fact-section .wpo-fun-fact-grids,
.wpo-fun-fact-section-s2 .wpo-fun-fact-grids {
  border: 1px solid #DADADA;
  padding: 20px;
}

.wpo-fun-fact-section .wpo-fun-fact-grids .grid,
.wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
  width: 25%;
  float: left;
  border-top: 1px solid #DADADA;
  border-bottom: 1px solid #DADADA;
  border-left: 1px solid #DADADA;
  padding: 30px 0;
  padding-bottom: 19px;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid,
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
    border: 1px solid #DADADA;
    border-bottom: 1px solid transparent;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid:nth-child(1),
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid:nth-child(1) {
    border-bottom: 1px solid transparent;
  }
}

.wpo-fun-fact-section .wpo-fun-fact-grids .grid:nth-child(4),
.wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid:nth-child(4) {
  border-right: 1px solid #DADADA;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid:nth-child(4),
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid:nth-child(4) {
    border-bottom: 1px solid #DADADA;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid:nth-child(2),
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid:nth-child(2) {
    border-right: 1px solid #DADADA;
    border-bottom: 1px solid transparent;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid,
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid,
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
    width: 100%;
    float: none;
  }
}

.wpo-fun-fact-section .grid h3,
.wpo-fun-fact-section-s2 .grid h3 {
  margin: 0 0 0.2em;
  font-family: "Epilogue";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 400;
  font-size: 50px;
  line-height: 60px;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #1C1817;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .grid h3,
  .wpo-fun-fact-section-s2 .grid h3 {
    line-height: 45px;
    font-size: 35px;
  }
}

.wpo-fun-fact-section .grid h3 + p,
.wpo-fun-fact-section-s2 .grid h3 + p {
  text-transform: uppercase;
  color: #1C1817;
  font-family: "Epilogue";
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .grid h3 + p,
  .wpo-fun-fact-section-s2 .grid h3 + p {
    font-size: 18px;
  }
}

.wpo-fun-fact-section {
  background: #F5F6E4;
  position: relative;
  z-index: 1;
}

.wpo-fun-fact-section:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-fun-fact-section .wpo-fun-fact-grids {
  border-color: #b1a9a7;
}

.wpo-fun-fact-section .wpo-fun-fact-grids .grid {
  border-color: #b1a9a7;
}

.wpo-fun-fact-section .wpo-fun-fact-grids .grid:nth-child(4) {
  border-color: #b1a9a7;
}

/* 3.11 partners-section   */
.partners-section {
  padding: 100px 0;
}

@media (max-width: 991px) {
  .partners-section {
    padding: 70px 0 50px;
  }
}

.partners-section .partners-slider .grid img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
  margin: 0 auto;
}

.partners-section .partners-slider .owl-item img {
  width: unset;
}

.partners-section .partners-slider .owl-nav {
  display: none;
}

/*=====================================================
7. wpo-service-single
======================================================*/
.wpo-service-single-area {
  padding-bottom: 80px;
}

@media (max-width: 991px) {
  .wpo-service-single-area {
    padding-bottom: 50px;
  }
}

@media (max-width: 767px) {
  .wpo-service-single-area {
    padding-bottom: 40px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-title h3 {
  font-weight: 400;
  font-size: 35px;
  line-height: 130.5%;
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-title h3 {
    font-size: 25px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-item {
  margin-bottom: 40px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-item .wpo-service-single-main-img {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-item p {
  color: #525252;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget {
  max-width: 590px;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul {
  list-style: none;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul li {
  padding: 10px 0;
  padding-left: 35px;
  position: relative;
  color: #525252;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  background: rgba(173, 185, 39, 0.2);
  content: "\e64c";
  font-family: "themify";
  text-align: center;
  color: #adb927;
  font-size: 14px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-p-details-img {
  margin-bottom: 10px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area {
  padding-bottom: 0;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item {
  padding: 20px;
  -webkit-box-shadow: 0px 2px 10px 2px rgba(21, 44, 88, 0.05);
          box-shadow: 0px 2px 10px 2px rgba(21, 44, 88, 0.05);
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item img {
  margin-bottom: 20px;
}

@media (max-width: 1200px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item {
    padding: 20px 10px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
  font-weight: 400;
  font-size: 22px;
  margin-bottom: 10px;
  margin-top: 0;
}

@media (max-width: 1400px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 19px;
    margin-bottom: 10px;
  }
}

@media (max-width: 1200px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 17px;
    margin-bottom: 10px;
  }
}

@media (max-width: 991px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 22px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item p {
  margin-bottom: 5px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area {
  padding: 30px;
  -webkit-box-shadow: 0px 2px 8px 2px rgba(22, 14, 71, 0.1);
          box-shadow: 0px 2px 8px 2px rgba(22, 14, 71, 0.1);
  border-radius: 20px;
  background: #fff;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-title {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .col {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .form-control {
  height: 50px;
  border: 0;
  border-bottom: 1px solid #DADADA;
  border-radius: 0;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area textarea.form-control {
  height: 120px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area select.form-control {
  background: transparent url(../images/select-icon2.png) no-repeat calc(100% - 15px) center;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .submit-area .theme-btn, .wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .submit-area .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .submit-area .theme-btn::before, .wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .submit-area .view-cart-btn::before {
  display: none;
  position: unset;
}

/*==============================================
8. property-single-pages
================================================*/
.wpo-hotel-details-section {
  padding: 80px 0;
}

.wpo-hotel-details-wrap {
  -webkit-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
  box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
  background: #fff;
  border-radius: 120px;
}

.wpo-hotel-details-area form {
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-hotel-details-area form input,
.wpo-hotel-details-area form select,
.wpo-hotel-details-area form button {
  width: 100%;
  font-size: 20px;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  color: #283a5e;
  font-weight: 700;
}

.wpo-hotel-details-area form input {
  padding-left: 0;
}

@media (max-width: 767px) {
  .wpo-hotel-details-area form input,
  .wpo-hotel-details-area form select,
  .wpo-hotel-details-area form button {
    height: 50px;
    font-size: 12px;
    /* font-size: 0.9375rem; */
  }
}

.wpo-hotel-details-area form select {
  border-radius: 70px 0 0 70px;
  padding: 0 25px;
  background: white;
  border-right: 1px solid #6cf5fd;
}

.wpo-hotel-details-area form input:focus,
.wpo-hotel-details-area form select:focus {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #283a5e;
}

.details-sub {
  margin-right: 20px;
  position: relative;
}

.details-sub:last-child {
  margin-right: 0;
}

.details-sub .fi:before {
  font-size: 20px;
}

.input-group-addon {
  background: none;
  border: none;
}

.wpo-hotel-details-area form button {
  font-weight: 600;
  color: #fff;
  z-index: 1;
  position: relative;
  top: 10px;
  right: 10px;
  font-size: 15px;
  width: 175px;
  font-size: 16px;
  padding: 15px 20px;
}

.wpo-hotel-details-area form button:focus {
  outline: none;
  border: none;
}

.details-sub span {
  text-transform: uppercase;
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  color: #687693;
}

.details-sub span i {
  margin-right: 10px;
}

.wpo-hotel-details-area .details-sub:before {
  position: absolute;
  left: -30px;
  top: 50%;
  width: 1px;
  height: 70px;
  background: #dddddd;
  content: "";
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.details-sub:first-child:before {
  display: none;
}

.details-sub:last-child:before {
  display: none;
}

.wpo-hotel-details-area input {
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

@media (max-width: 767px) {
  .wpo-hotel-details-area .nice-select {
    height: 50px;
    line-height: 50px;
  }
}

.wpo-hotel-details-area {
  padding: 50px 20px 20px;
}

@media (max-width: 590px) {
  .wpo-hotel-details-area {
    padding: 20px 2px;
    padding-bottom: 20px;
  }
}

@media (max-width: 1200px) {
  .wpo-hotel-details-area form input,
  .wpo-hotel-details-area form select {
    font-size: 20px;
  }
  .wpo-hotel-details-area .nice-select span {
    font-size: 20px;
  }
  .details-sub span {
    font-size: 15px;
  }
  .wpo-hotel-details-area form button {
    font-size: 15px;
  }
  .details-sub:first-child {
    width: 145px;
  }
  .details-sub:nth-child(2) {
    width: 160px;
  }
}

@media (max-width: 992px) {
  .details-sub span {
    font-size: 12px;
  }
  .details-sub .fi:before {
    font-size: 14px;
  }
  .wpo-hotel-details-area form input,
  .wpo-hotel-details-area form select {
    font-size: 14px;
  }
  .details-sub:first-child {
    width: 125px;
  }
  .details-sub:nth-child(2) {
    width: 125px;
  }
  .wpo-hotel-details-area .nice-select span {
    font-size: 15px;
  }
  .wpo-hotel-details-area .details-sub:before {
    display: none;
  }
  .hero-style-1 {
    margin: 0;
  }
}

@media (max-width: 767px) {
  .wpo-hotel-details-area form {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .wpo-hotel-details-wrap {
    margin-top: 0;
    padding: 20px;
  }
  .wpo-select-section {
    padding-top: 80px;
  }
}

@media (max-width: 550px) {
  .details-sub {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #ddd;
  }
  .details-sub:first-child {
    width: 100%;
  }
  .details-sub:nth-child(2) {
    width: 100%;
  }
  .details-sub:last-child {
    border-bottom: 0;
  }
}

.details-sub h2 {
  font-size: 20px;
  margin-bottom: 0;
}

.wpo-hotel-details-section .details-sub span {
  font-size: 16px;
}

.wpo-hotel-details-section .details-sub {
  -ms-flex-preferred-size: 14%;
      flex-basis: 14%;
  text-align: center;
}

@media (max-width: 991px) {
  .wpo-hotel-details-section .details-sub {
    -ms-flex-preferred-size: 22%;
        flex-basis: 22%;
  }
}

@media (max-width: 650px) {
  .wpo-hotel-details-section .details-sub {
    -ms-flex-preferred-size: 46%;
        flex-basis: 46%;
    padding-bottom: 20px;
  }
}

@media (max-width: 560px) {
  .wpo-hotel-details-section .details-sub {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
  }
}

.wpo-hotel-details-section .details-sub:last-child {
  -ms-flex-preferred-size: 18%;
      flex-basis: 18%;
}

@media (max-width: 991px) {
  .wpo-hotel-details-section .details-sub:last-child {
    -ms-flex-preferred-size: 35%;
        flex-basis: 35%;
  }
}

@media (max-width: 650px) {
  .wpo-hotel-details-section .details-sub:last-child {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
  }
}

@media (max-width: 560px) {
  .wpo-hotel-details-section .details-sub:last-child {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
  }
}

.wpo-hotel-details-section .container {
  width: 1425px;
  max-width: 100%;
}

.wpo-hotel-details-section .details-sub h5 {
  margin-top: 0;
  font-size: 18px;
  color: #687693;
  margin-bottom: 10px;
}

.wpo-hotel-details-section .details-sub h5 span {
  font-size: 30px;
  color: #adb927;
  font-weight: 700;
  display: block;
  margin-top: 5px;
}

.wpo-hotel-details-section .details-sub:last-child {
  text-align: center;
  top: 10px;
}

.wpo-hotel-details-section .details-sub:last-child:before {
  display: block;
}

@media (max-width: 991px) {
  .wpo-hotel-details-section .details-sub:last-child:before {
    display: none;
  }
}

.wpo-hotel-details-section .wpo-hotel-details-area {
  padding: 50px 20px;
}

.property-title h2 {
  margin-top: 0;
  font-size: 35px;
  color: #283a5e;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebebeb;
  margin-bottom: 50px;
  font-weight: 700;
}

@media (max-width: 991px) {
  .property-title h2 {
    font-size: 25px;
    margin-bottom: 30px;
  }
}

.property-description p {
  margin-bottom: 40px;
}

.property-description .p-wrap {
  font-size: 20px;
  color: #384f7c;
}

.property-d-text .property-title h2 {
  margin-bottom: 10px;
}

.property-d-text ul li a {
  color: #283a5e;
  display: block;
  padding: 7px 0;
  position: relative;
  padding-left: 20px;
}

.property-d-text ul li a:before {
  position: absolute;
  left: 0;
  top: 6px;
  content: "\e64c";
  font-family: "themify";
}

.property-details-service {
  margin-top: 20px;
}

.property-d-img {
  margin-bottom: 60px;
}

.map-area {
  margin-top: 60px;
}

.map-area iframe {
  width: 100%;
  height: 350px;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

.property-review {
  padding: 80px 0;
}

@media (max-width: 991px) {
  .property-review {
    padding-bottom: 0;
  }
}

.review-item .review-img {
  float: left;
  margin-right: 40px;
}

.review-text {
  overflow: hidden;
}

.review-text h2 {
  font-size: 22px;
  color: #283a5e;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 25px;
}

.review-text ul {
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 20px;
  list-style: none;
}

.review-text ul li {
  margin-right: 5px;
}

.review-text ul li i:before {
  font-size: 20px;
  color: #e0a403;
}

.r-title {
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
}

.r-title h2 {
  margin-bottom: 0;
}

.review-text p {
  line-height: 35px;
}

.review-item {
  margin-bottom: 50px;
}

.add-review .comment-respond {
  margin-top: 30px !important;
}

.add-review .review-text ul {
  margin-left: 0;
}

.shipp input[type="checkbox"] {
  display: none;
}

.shipp input[type="checkbox"] + label span {
  display: inline-block;
  width: 25px;
  height: 25px;
  vertical-align: middle;
  background: #fff;
  position: relative;
  margin-right: 10px;
  margin-top: -2px;
  border: 1px solid #999999;
  line-height: 25px;
  text-align: center;
}

.shipp input[type="checkbox"] + label span:before {
  content: "\e64c";
  font-family: 'themify';
  position: absolute;
  color: #fff;
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
  top: -2px;
  left: 5px;
}

.shipp input[type="checkbox"]:checked + label span {
  background-color: #adb927;
  border-color: transparent;
}

.shipp input[type="checkbox"]:checked + label span:before {
  visibility: visible;
  opacity: 1;
}

.form-check {
  margin: 25px 0 40px;
}

.shipp label {
  color: #283a5e;
  font-weight: 400;
}

.other-property {
  padding: 80px 0 100px;
}

.other-property .container {
  padding: 0;
}

@media (max-width: 1400px) {
  .wpo-hotel-details-section .details-sub span {
    font-size: 13px;
  }
  .details-sub h2 {
    font-size: 15px;
  }
  .wpo-hotel-details-section .details-sub h5 span {
    font-size: 30px;
  }
  .wpo-hotel-details-section .details-sub h5 {
    font-size: 14px;
  }
  .wpo-hotel-details-area form button {
    font-size: 12px;
    width: 152px;
  }
}

@media (max-width: 992px) {
  .property-details-item {
    padding: 0 15px;
  }
  .property-d-img img {
    width: 100%;
  }
}

@media (max-width: 767px) {
  .wpo-hotel-details-section .details-sub:last-child {
    margin-top: 40px;
  }
  .wpo-hotel-details-section {
    padding-top: 60px;
  }
  .wpo-hotel-details-section .wpo-hotel-details-wrap {
    padding: 0px;
  }
}

@media (max-width: 650px) {
  .wpo-hotel-details-section .details-sub:first-child {
    width: 100%;
  }
  .wpo-hotel-details-section .details-sub:nth-child(2) {
    width: 100%;
  }
  .wpo-hotel-details-section .details-sub {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #ddd;
  }
  .wpo-hotel-details-section .details-sub:last-child {
    border-bottom: 0;
    margin-bottom: 0;
    margin-top: 0;
    padding-bottom: 0;
  }
}

@media (max-width: 540px) {
  .review-item .review-img {
    float: none;
    margin-bottom: 20px;
  }
  .review-text h2 {
    font-size: 20px;
  }
}

.property-d-text2 .property-title h2 {
  margin-bottom: 10px;
}

.property-d-text ul,
.property-d-text2 ul {
  list-style: none;
}

.property-d-text2 ul li a {
  color: #283a5e;
  display: block;
  padding: 7px 0;
  position: relative;
  padding-left: 20px;
}

.property-d-text2 ul li a:before {
  position: absolute;
  left: 0;
  top: 6px;
  content: "\e64c";
  font-family: "themify";
}

.property-d-img img {
  width: 100%;
}

.property-d-text,
.property-d-text2 {
  -webkit-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
  box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
  padding: 20px;
  margin-top: 30px;
  margin-right: -100px;
  position: relative;
  z-index: 9;
  background: #fff;
}

.property-d-text2 {
  margin-right: 0;
  margin-left: -100px;
}

.property-d-text ul li a:before,
.property-d-text2 ul li a:before {
  top: 7px;
  content: "\e64c";
  background: rgba(229, 243, 70, 0.2);
  width: 25px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  border-radius: 50%;
  color: #adb927;
  font-size: 14px;
}

.property-d-text ul li a,
.property-d-text2 ul li a {
  padding-left: 35px;
}

.property-d-img img {
  min-height: 430px;
  -o-object-fit: cover;
     object-fit: cover;
}

@media (max-width: 767px) {
  .property-d-text,
  .property-d-text2 {
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 60px;
  }
  .property-d-text2 {
    margin-right: 0;
    margin-left: 0px;
  }
}

.wpo-property-section.s3 .property-slider-s2 .owl-nav [class*=owl-] {
  top: 35%;
  border-radius: 50%;
  left: 15px;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-property-section.s3 .property-slider-s2 .owl-nav [class*=owl-].owl-next {
  right: 0;
  left: auto;
}

.wpo-property-section.s3 .property-slider-s2:hover .owl-nav [class*=owl-] {
  -webkit-transform: scale(1);
          transform: scale(1);
}

.wpo-property-section-s2 {
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .wpo-property-section-s2 {
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-property-section-s2 {
    padding-bottom: 50px;
  }
}

.wpo-property-section-s2 .card-single {
  margin-bottom: 30px;
}

/*==========================================
9. wpo-team-single
===========================================*/
.wpo-team-section-s2 {
  background: none;
}

.wpo-team-section-s2::after, .wpo-team-section-s2::before {
  display: none;
}

.wpo-team-section-s2 .wpo-team-wrap-s3 .wpo-team-item {
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
}

.team-pg-area {
  max-width: 1080px;
  margin: 0 auto;
}

.team-info-img {
  padding: 25px;
  -webkit-box-shadow: 0px 6px 20.1px 4.9px rgba(176, 191, 238, 0.12);
          box-shadow: 0px 6px 20.1px 4.9px rgba(176, 191, 238, 0.12);
}

@media (max-width: 991px) {
  .team-info-img {
    margin-bottom: 40px;
  }
}

.team-info-img img {
  width: 100%;
  height: 465px;
  -o-object-fit: cover;
     object-fit: cover;
}

@media (max-width: 991px) {
  .team-info-img img {
    height: 100%;
  }
}

.team-info-text h2 {
  font-size: 30px;
  font-weight: 400;
  color: #fff;
  margin-top: 0;
  margin-bottom: 50px;
  text-align: center;
}

.team-info-text {
  -webkit-box-shadow: 0px 6px 20.1px 4.9px rgba(176, 191, 238, 0.12);
          box-shadow: 0px 6px 20.1px 4.9px rgba(176, 191, 238, 0.12);
  padding: 58px 50px;
}

.team-info-text ul li {
  font-size: 17px;
  color: #1d2327;
  font-weight: 400;
  margin-bottom: 22px;
  list-style: none;
}

.team-info-text ul li:last-child {
  padding-bottom: 0;
}

.team-info-text ul li span {
  font-size: 16px;
  color: #8c8c8c;
  display: inline-block;
  position: relative;
  padding-left: 5px;
  font-weight: 400;
}

.team-info-text ul li:last-child {
  margin-bottom: 0;
}

.at-progress {
  -webkit-box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
  padding: 40px 0;
}

@media (max-width: 991px) {
  .at-progress {
    padding-bottom: 0;
  }
}

.team-info-text h2 {
  font-size: 27px;
  font-weight: 400;
  color: #1C1817;
  margin-top: 0;
  margin-bottom: 25px;
  text-align: center;
  background: #eee;
  padding: 20px;
}

.team-info-wrap ul {
  padding-left: 0;
}

.exprience-wrap p {
  color: #5d5851;
}

.exprience-wrap h2 {
  margin-bottom: 20px;
  font-weight: 400;
  color: #1C1817;
}

/*progress-design*/
.progress {
  width: 150px;
  height: 150px;
  background: none;
  margin: 0 auto;
  -webkit-box-shadow: none;
          box-shadow: none;
  position: relative;
  overflow: unset;
}

.progress:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 6px solid #ebebeb;
  position: absolute;
  top: 0;
  left: 0;
}

.progress > span {
  width: 50%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  z-index: 1;
}

.progress .progress-left {
  left: 0;
}

.progress .progress-bar {
  width: 100%;
  height: 100%;
  background: none;
  border-width: 6px;
  border-style: solid;
  position: absolute;
  top: 0;
}

.progress .progress-left .progress-bar {
  left: 100%;
  border-top-right-radius: 80px;
  border-bottom-right-radius: 80px;
  border-left: 0;
  -webkit-transform-origin: center left;
  transform-origin: center left;
}

.progress .progress-right {
  right: 0;
}

.progress .progress-right .progress-bar {
  left: -100%;
  border-top-left-radius: 80px;
  border-bottom-left-radius: 80px;
  border-right: 0;
  -webkit-transform-origin: center right;
  transform-origin: center right;
  -webkit-animation: loading-1 1.8s linear forwards;
          animation: loading-1 1.8s linear forwards;
}

.progress .progress-value {
  width: 90%;
  height: 90%;
  border-radius: 50%;
  background: #fff;
  font-size: 30px;
  color: #1C1817;
  line-height: 135px;
  text-align: center;
  position: absolute;
  top: -5%;
  left: 5%;
  font-weight: 400;
  font-family: "Epilogue";
}

.progress .progress-bar {
  border-color: #adb927;
}

.progress-name {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  bottom: 25%;
  width: 100%;
  text-align: center;
}

.progress-name span {
  font-size: 15px;
  color: #131058;
  line-height: 15px;
}

.progress.blue .progress-left .progress-bar {
  -webkit-animation: loading-2 1.5s linear forwards 1.8s;
          animation: loading-2 1.5s linear forwards 1.8s;
}

.progress.yellow .progress-left .progress-bar {
  -webkit-animation: loading-3 1s linear forwards 1.8s;
          animation: loading-3 1s linear forwards 1.8s;
}

.progress.pink .progress-left .progress-bar {
  -webkit-animation: loading-4 0.4s linear forwards 1.8s;
          animation: loading-4 0.4s linear forwards 1.8s;
}

.progress.green .progress-left .progress-bar {
  -webkit-animation: loading-5 1.2s linear forwards 1.8s;
          animation: loading-5 1.2s linear forwards 1.8s;
}

@-webkit-keyframes loading-1 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

@keyframes loading-1 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

@-webkit-keyframes loading-2 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@keyframes loading-2 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@-webkit-keyframes loading-3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(120deg);
    transform: rotate(120deg);
  }
}

@keyframes loading-3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(120deg);
    transform: rotate(120deg);
  }
}

@-webkit-keyframes loading-4 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(46deg);
    transform: rotate(46deg);
  }
}

@keyframes loading-4 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(46deg);
    transform: rotate(46deg);
  }
}

@-webkit-keyframes loading-5 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(36deg);
    transform: rotate(36deg);
  }
}

@keyframes loading-5 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(36deg);
    transform: rotate(36deg);
  }
}

@media only screen and (max-width: 990px) {
  .progress {
    margin-bottom: 60px;
  }
}

.progress:before {
  position: absolute;
  left: -14px;
  top: -15px;
  width: 120%;
  height: 120%;
  background: #fff;
  content: "";
  border-radius: 50%;
}

.education-area {
  max-width: 600px;
}

.education-area ul {
  list-style: none;
}

.education-area ul li {
  padding-bottom: 15px;
  position: relative;
  padding-left: 20px;
  color: #525252;
}

.education-area ul li:before {
  position: absolute;
  left: 0;
  top: 5px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #E5F346;
  content: "";
}

.ex-wiget {
  padding-top: 60px;
}

.ex-wiget ul {
  list-style: none;
}

.ex-wiget ul li {
  color: #525252;
}

.ex-wiget h2 {
  font-size: 30px;
  font-weight: 400;
  color: #1C1817;
  margin-bottom: 30px;
}

.wpo-contact-area .quote-form {
  padding-left: 0;
  margin-left: -10px;
}

.wpo-contact-area button {
  border-radius: 0;
}

.wpo-contact-area button:after {
  background: #1C1817;
}

.wpo-contact-area .quote-form .form-control {
  height: 55px;
  border: 1px solid #ebebeb;
  margin-bottom: 20px;
  padding: 15px;
}

.wpo-contact-area .quote-form .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #E5F346;
}

.wpo-contact-area .quote-form textarea.form-control {
  height: 125px;
}

.half-col {
  float: left;
  width: 50%;
  padding: 0 10px 5px;
}

.full-col {
  padding: 0 10px 5px;
}

.full-col .theme-btn, .full-col .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.full-col .theme-btn::before, .full-col .view-cart-btn::before {
  display: none;
  position: unset;
}

.exprience-area {
  padding-top: 60px;
}

@media (max-width: 767px) {
  .team-info-text {
    padding: 40px 20px;
  }
  .team-info-img {
    padding: 40px;
  }
  .team-info-img img {
    width: 100%;
  }
  .custom-grid {
    float: left;
    width: 50%;
  }
  .exprience-area {
    padding-top: 30px;
  }
}

@media (max-width: 575px) {
  .half-col {
    float: none;
    width: 100%;
  }
  .lawyer-about.pt-150 {
    padding-top: 100px !important;
  }
}

@media (max-width: 375px) {
  .custom-grid {
    float: none;
    width: 100%;
  }
}

.service-sidebar .wpo-contact-widget {
  border: 0;
  background: url(../images/bg-2.html) no-repeat center center;
  position: relative;
  background-size: cover;
  z-index: 1;
}

.service-sidebar .wpo-contact-widget:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(22, 57, 103, 0.9);
  content: "";
  z-index: -1;
}

.service-sidebar .wpo-contact-widget h2 {
  font-size: 36px;
  font-weight: 400;
  text-align: left;
  color: #fff;
  margin-bottom: 20px;
  color: #1C1817;
}

.service-sidebar .wpo-contact-widget h2::before {
  background: #fff;
}

.service-sidebar .wpo-contact-widget p {
  color: #fff;
  font-size: 18px;
}

.service-sidebar .wpo-contact-widget a {
  display: inline-block;
  padding: 10px 20px;
  border: 1px solid #fff;
  font-size: 18px;
  color: #fff;
  padding-right: 90px;
  position: relative;
  margin-top: 10px;
}

.service-sidebar .wpo-contact-widget a::before {
  font-family: "themify";
  content: "\e628";
  font-size: 18px;
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

/*--------------------------------------------------------------
10. wpo-shop-page
--------------------------------------------------------------*/
.wpo-shop-section .shop-grids {
  margin: 0 -15px;
}

@media (max-width: 767px) {
  .wpo-shop-section .shop-grids {
    margin: 0 -7px;
  }
}

.wpo-shop-section .grid {
  width: calc(33.33% - 30px);
  float: left;
  margin: 0 15px 30px;
}

@media (max-width: 991px) {
  .wpo-shop-section .grid {
    width: calc(50% - 30px);
  }
}

@media (max-width: 767px) {
  .wpo-shop-section .grid {
    width: calc(50% - 15px);
    margin: 0 7px 15px;
  }
}

@media (max-width: 550px) {
  .wpo-shop-section .grid {
    width: calc(100% - 15px);
    float: none;
  }
}

.wpo-shop-section .grid img {
  width: 100%;
}

.wpo-shop-section .details {
  -webkit-box-shadow: 0px 2px 10px 2px rgba(21, 44, 88, 0.05);
          box-shadow: 0px 2px 10px 2px rgba(21, 44, 88, 0.05);
  text-align: center;
  padding: 35px 15px;
}

.wpo-shop-section .details h3 {
  font-size: 20px;
  margin: 0 0 0.5em;
}

@media (max-width: 991px) {
  .wpo-shop-section .details h3 {
    font-size: 16px;
  }
}

.wpo-shop-section .details h3 a {
  color: #1C1817;
  font-family: "Epilogue";
}

.wpo-shop-section .details h3 a:hover {
  color: #adb927;
}

.wpo-shop-section .details del {
  color: #424740;
}

.wpo-shop-section .details del + span {
  display: inline-block;
  padding-left: 20px;
}

.wpo-shop-section .add-to-cart {
  margin-top: 20px;
}

.wpo-shop-section .add-to-cart a {
  font-family: "Sora", sans-serif;
  font-size: 15px;
  font-size: 1rem;
  font-weight: 600;
  color: #adb927;
  padding: 12px 20px;
  border: 2px solid #adb927;
  display: inline-block;
  border-radius: 50px;
}

@media (max-width: 767px) {
  .wpo-shop-section .add-to-cart a {
    font-size: 12px;
    padding: 8px 18px;
  }
}

.wpo-shop-section .add-to-cart a:hover {
  background-color: #adb927;
  color: #fff;
}

.wpo-shop-section .add-to-cart a i {
  font-size: 15px;
  display: inline-block;
  padding-left: 5px;
}

.wpo-shop-section .grid:hover .add-to-cart a {
  background-color: #adb927;
  color: #fff;
}

/*--------------------------------------------------------------
11. wpo-shop-single-page
--------------------------------------------------------------*/
.wpo-shop-single-section {
  /*** product slider ***/
  /*** product info ***/
}

.wpo-shop-single-section .shop-single-slider .slider-for {
  text-align: center;
}

.wpo-shop-single-section .shop-single-slider .slider-for img {
  display: inline-block;
}

.wpo-shop-single-section .shop-single-slider .slider-nav {
  padding: 0 25px;
  margin-top: 35px;
}

.wpo-shop-single-section .shop-single-slider .slider-nav > i {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 100;
}

.wpo-shop-single-section .shop-single-slider .slider-nav > i:hover {
  cursor: pointer;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .nav-btn-rt {
  left: auto;
  right: 0;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .slick-slide {
  text-align: center;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .slick-slide img {
  display: inline-block;
}

.wpo-shop-single-section .product-details {
  padding: 15px 30px 80px;
  /*** product option ***/
}

@media (max-width: 1199px) {
  .wpo-shop-single-section .product-details {
    padding: 10px 30px 15px;
  }
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details {
    margin-top: 45px;
    padding: 40px 30px;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details {
    padding: 0;
  }
}

.wpo-shop-single-section .product-details h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  line-height: 1.4em;
  margin: 0 0 0.33em;
  font-weight: 500;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details h2 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

.wpo-shop-single-section .product-details .price {
  font-size: 25px;
  font-size: 1.66667rem;
  color: #adb927;
  margin: 7px 0 20px;
  font-weight: 500;
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details .price {
    font-size: 30px;
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .price {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-shop-single-section .product-details .price .old {
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: normal;
  color: #747d70;
  text-decoration: line-through;
  display: inline-block;
  margin-left: 5px;
  font-weight: 500;
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details .price .old {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .price .old {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

.wpo-shop-single-section .product-details .product-rt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
}

.wpo-shop-single-section .product-details .product-rt .rating {
  margin-right: 10px;
}

.wpo-shop-single-section .product-details .product-rt .rating i {
  color: #adb927;
}

.wpo-shop-single-section .product-details .product-rt span {
  color: #525252;
}

.wpo-shop-single-section .product-details p {
  margin-bottom: 1.3em;
  color: #525252;
}

.wpo-shop-single-section .product-details ul {
  list-style: none;
}

.wpo-shop-single-section .product-details ul li {
  padding-left: 20px;
  position: relative;
  margin-bottom: 10px;
  color: #525252;
}

.wpo-shop-single-section .product-details ul li:before {
  position: absolute;
  left: 0;
  top: 6px;
  width: 8px;
  height: 8px;
  background: #adb927;
  content: "";
  border-radius: 50%;
}

.wpo-shop-single-section .product-details .product-option {
  margin-top: 30px;
}

.wpo-shop-single-section .product-details .tg-btm {
  margin-top: 25px;
}

.wpo-shop-single-section .product-details .tg-btm p {
  margin-bottom: 0px;
}

.wpo-shop-single-section .product-details .tg-btm p span {
  font-weight: 500;
  color: #1C1817;
  margin-right: 5px;
}

.wpo-shop-single-section .product-details .product-option .product-row button:after {
  display: none;
}

.wpo-shop-single-section .product-details .product-option .product-row > div {
  height: 35px;
  display: inline-block;
}

.wpo-shop-single-section .product-details .product-option .product-row > div + div {
  margin-left: 15px;
}

.wpo-shop-single-section .product-details .product-option .product-row > div + div .theme-btn, .wpo-shop-single-section .product-details .product-option .product-row > div + div .view-cart-btn {
  border-radius: 0;
  border-width: 1px;
  height: 40px;
  padding: 0 18px;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:first-child {
  width: 85px;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:last-child .theme-btn:hover, .wpo-shop-single-section .product-details .product-option .product-row > div:last-child .view-cart-btn:hover {
  background: #adb927;
  border-color: #adb927;
  color: #fff;
}

.wpo-shop-single-section .product-details .product-option .theme-btn, .wpo-shop-single-section .product-details .product-option .view-cart-btn {
  background-color: transparent;
  color: #333;
  font-size: 14px;
  font-size: 0.93333rem;
  padding: 0 20px;
  height: 35px;
  line-height: 32px;
  outline: 0;
  border: 2px solid #e6e6e6;
  border-radius: 0;
}

.wpo-shop-single-section .product-details .product-option .theme-btn:hover, .wpo-shop-single-section .product-details .product-option .view-cart-btn:hover {
  background-color: #E5F346;
  color: #1C1817;
  border-color: #E5F346;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .product-option .theme-btn, .wpo-shop-single-section .product-details .product-option .view-cart-btn {
    font-size: 14px;
    font-size: 0.93333rem;
  }
}

.wpo-shop-single-section .product-details .product-option .theme-btn:before, .wpo-shop-single-section .product-details .product-option .view-cart-btn:before {
  display: none;
}

.wpo-shop-single-section .product-details .product-option .heart-btn i {
  font-size: 15px;
  font-size: 1rem;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:last-child .theme-btn, .wpo-shop-single-section .product-details .product-option .product-row > div:last-child .view-cart-btn {
  background-color: #fff;
  font-size: 18px;
  font-size: 1.2rem;
  color: #424740;
  border: 1px solid #e6e6e6;
}

.wpo-shop-single-section .product-details #product-count {
  border-radius: 0;
  border: 1px solid #e6e6e6;
}

.wpo-shop-single-section .product-details #product-count:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up,
.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  border-radius: 0;
  border-color: #e6e6e6;
  display: block;
  padding: 1px 4px;
  font-size: 10px;
}

.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up:hover,
.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down:hover {
  background-color: #E5F346;
  color: #1C1817;
}

.wpo-shop-single-section .product-info {
  margin-top: 75px;
  /*** tabs ***/
  /*** client rv ***/
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-info {
    margin-top: 40px;
  }
}

.wpo-shop-single-section .product-info h4 {
  font-size: 18px;
  font-size: 1.2rem;
  margin: 0;
  line-height: 1.7em;
  font-weight: 500;
  margin-bottom: 10px;
}

.wpo-shop-single-section .product-info p {
  margin-bottom: 1.3em;
  color: #525252;
}

.wpo-shop-single-section .product-info .tab-pane p:last-child {
  margin-bottom: 0;
}

.wpo-shop-single-section .product-info .nav-tabs {
  font-family: "Epilogue";
  border: 0;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .nav-tabs {
    margin-bottom: 20px;
  }
}

.wpo-shop-single-section .product-info .nav-tabs li {
  margin-right: 1px;
}

.wpo-shop-single-section .product-info .nav-tabs li a.active {
  border: 0;
  outline: 0;
}

.wpo-shop-single-section .product-info .nav-tabs a {
  font-size: 15px;
  font-size: 1rem;
  color: #424740;
  border: 0;
  border-radius: 0;
  margin: 0;
  display: block;
  padding: 12px 20px 11px;
  font-family: "Sora", sans-serif;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .nav-tabs a {
    font-size: 14px;
    font-size: 0.93333rem;
    font-weight: normal;
    padding: 10px 10px 8px;
    text-transform: none;
  }
}

.wpo-shop-single-section .product-info .nav-tabs a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 3px;
  background: #E5F346;
  border-radius: 4px;
  content: "";
  opacity: 0;
  visibility: hidden;
}

.wpo-shop-single-section .product-info .nav-tabs a:hover:before,
.wpo-shop-single-section .product-info .nav-tabs a.active:before {
  opacity: 1;
  visibility: visible;
}

.wpo-shop-single-section .product-info .nav-tabs .active,
.wpo-shop-single-section .product-info .nav-tabs li:hover {
  border-color: #E5F346;
}

.wpo-shop-single-section .product-info .tab-content {
  border-top: 1px solid rgba(229, 243, 70, 0.3);
  padding-top: 30px;
  margin-top: -1px;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .tab-content {
    border: 0;
    padding: 0;
    margin: 0;
  }
}

.wpo-shop-single-section .product-info .client-rv {
  overflow: hidden;
  margin-bottom: 30px;
}

.wpo-shop-single-section .product-info .client-rv:last-child {
  margin-bottom: 0;
}

.wpo-shop-single-section .product-info .client-rv .client-pic {
  width: 100px;
  float: left;
}

.wpo-shop-single-section .product-info .client-rv .client-pic img {
  border-radius: 50%;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .client-pic {
    width: 100%;
    float: none;
    margin-bottom: 10px;
  }
}

.wpo-shop-single-section .product-info .client-rv .details {
  width: calc(100% - 130px);
  float: right;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .details {
    width: 100%;
    float: none;
  }
}

.wpo-shop-single-section .product-info .client-rv .name-rating .product-rt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-shop-single-section .product-info .client-rv .name-rating .product-rt span {
  color: #525252;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .name-rating-time {
    padding-bottom: 3px;
  }
}

.wpo-shop-single-section .product-info .client-rv .name-rating-time > div,
.wpo-shop-single-section .product-info .client-rv .name-rating > div {
  display: inline-block;
  font-size: 14px;
  font-size: 0.93333rem;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .name-rating-time > div,
  .wpo-shop-single-section .product-info .client-rv .name-rating > div {
    font-size: 12px;
    font-size: 0.8rem;
    display: block;
  }
}

.wpo-shop-single-section .product-info .client-rv .rating {
  font-size: 12px;
  padding-left: 12px;
}

.wpo-shop-single-section .product-info .client-rv .rating i {
  color: #E5F346;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .rating {
    padding-left: 0;
    margin: 4px 0 7px;
  }
}

.wpo-shop-single-section .product-info .client-rv .name-rating-time .time {
  float: right;
  color: #b3b3b3;
  text-transform: uppercase;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .client-rv .name-rating-time .time {
    float: none;
  }
}

.wpo-shop-single-section .product-info .client-rv .review-body {
  padding-top: 12px;
}

.wpo-shop-single-section .slider-nav .slick-slide:focus {
  outline: none;
}

.wpo-shop-single-section .submit .theme-btn-s2 {
  border: 0;
  padding: 10px 15px;
  background: #adb927;
  text-transform: capitalize;
  text-decoration: none;
  font-size: 16px;
  color: #1C1817;
}

/*** review form ***/
.review-form {
  margin-top: 45px;
}

.review-form h4 {
  font-size: 30px;
  font-weight: 500;
}

.review-form p {
  margin-bottom: 1.73em;
  color: #525252;
}

.review-form .give-rat-sec {
  margin-bottom: 10px;
}

.review-form .give-rat-sec p {
  margin-bottom: 0;
  color: #525252;
}

.review-form .give-rating {
  display: inline-block;
  position: relative;
  height: 50px;
  line-height: 50px;
  font-size: 30px;
  margin-bottom: 10px;
}

.review-form .give-rating label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  cursor: pointer;
}

.review-form .give-rating label:last-child {
  position: static;
}

.review-form .give-rating label:nth-child(1) {
  z-index: 5;
}

.review-form .give-rating label:nth-child(2) {
  z-index: 4;
}

.review-form .give-rating label:nth-child(3) {
  z-index: 3;
}

.review-form .give-rating label:nth-child(4) {
  z-index: 2;
}

.review-form .give-rating label:nth-child(5) {
  z-index: 1;
}

.review-form .give-rating label input {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.review-form .give-rating label .icon {
  float: left;
  color: transparent;
}

.review-form .give-rating label:last-child .icon {
  color: #ddd;
}

.review-form .give-rating:not(:hover) label input:checked ~ .icon,
.review-form .give-rating:hover label:hover input ~ .icon {
  color: #FFD400;
}

.review-form .give-rating label input:focus:not(:checked) ~ .icon:last-child {
  color: #ddd;
  text-shadow: 0 0 5px #FFD400;
}

.review-form form input,
.review-form form textarea {
  background: transparent;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  height: 50px;
  border: 1px solid #efefef;
}

.review-form form input:focus,
.review-form form textarea:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.review-form form textarea {
  height: 130px;
}

.review-form form > div {
  margin-bottom: 27px;
}

.review-form form > div:last-child {
  margin-bottom: 0;
}

.review-form form .theme-btn-s4 {
  background-color: transparent;
  color: #908f8f;
  outline: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  padding: 10px 15px;
}

.review-form form .theme-btn-s4:hover {
  background-color: #E5F346;
  color: #fff;
}

@media screen and (min-width: 767px) {
  .review-form form .theme-btn-s4 {
    font-size: 15px;
    font-size: 1rem;
    padding: 0 20px;
  }
}

/*===========================
12. wpo-cart-page-style
===========================*/
.cart-area .cart-wrapper {
  -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
}

@media (max-width: 991px) {
  .cart-area .cart-wrapper table {
    width: 1022px;
  }
}

@media (max-width: 991px) {
  .cart-area .cart-wrapper form {
    overflow: scroll;
    overflow-y: hidden;
  }
}

.cart-area .cart-wrap td,
.cart-area .cart-wrap th {
  width: 10%;
  border-bottom: 1px solid #f0f0f094;
  text-align: center;
  font-weight: 400;
  color: #414141;
}

.cart-area .cart-wrap td ul,
.cart-area .cart-wrap th ul {
  list-style: none;
}

.cart-area .cart-wrap th {
  border-bottom: 1px solid #f0f0f094;
  padding: 40px 0;
}

.cart-area .cart-wrap thead {
  background: #fff;
  color: #505050;
  padding: 40px;
}

.cart-area .cart-area form {
  margin: auto;
  text-align: center;
  background: #fff;
}

.cart-area .cart-wrap .product {
  width: 15%;
}

.cart-area .cart-wrap .product ul {
  text-align: left;
  padding-left: 30px;
  list-style: none;
}

.cart-area .cart-wrap .product ul li {
  padding-bottom: 5px;
}

.cart-area .cart-wrap .product a {
  font-size: 16px;
  color: #a5a4a4;
}

.cart-area .cart-wrap .product-2 {
  text-align: left;
  padding-left: 55px;
}

.cart-area .cart-wrap .product a:hover {
  color: #E5F346;
}

.cart-area .cart-wrap th {
  height: 80px;
  font-weight: 700;
}

.cart-area .cart-wrap td {
  padding: 40px 25px;
}

.cart-area .cart-wrap td img {
  width: 130px;
  height: 130px;
  -o-object-fit: cover;
     object-fit: cover;
}

.cart-area .cart-wrap td.action ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  list-style: none;
}

.cart-area .cart-wrap td.action ul .w-btn-view a {
  line-height: 45px;
}

.cart-area .cart-wrap td span {
  display: block;
  width: 100px;
  height: 38px;
  line-height: 34px;
  color: #ee9902;
  font-size: 14px;
  border: 1.5px solid #ee9902;
  border-radius: 4px;
}

.cart-area .cart-wrap td.Del span {
  color: #4ABA4E;
  border: 1.5px solid #4ABA4E;
}

.cart-area .cart-wrap td.can span {
  color: #D85656;
  border: 1.5px solid #D85656;
}

.cart-area .cart-wrap td.pro span {
  color: #691A5F;
  border: 1.5px solid #691A5F;
}

.cart-area .cart-wrap .name {
  width: 15%;
}

.cart-area .cart-wrap .action a {
  display: block;
  width: 40px;
  height: 38px;
  line-height: 45px;
  background: #414141;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}

.cart-area .cart-wrap .action a .fi::before {
  font-size: 20px;
}

.cart-area .cart-wrap .action a:hover {
  background: #E5F346;
}

.cart-area .cart-wrap .action li.c-btn {
  margin-right: 10px;
}

.cart-area .cart-wrap .action li.c-btn a {
  background-color: #E5F346;
}

.cart-area .order-wrap {
  padding: 0;
}

.cart-area .cart-wrap tr:nth-child(even) {
  background: #FCFCFC;
}

.cart-area .cart-wrap .quantity {
  position: relative;
  max-width: 110px;
  margin: 0 auto;
}

.cart-area .quantity input {
  width: 105px;
  padding: 0px 35px;
  text-align: center;
  height: 36px;
  position: relative;
  background: #f2f2f5;
  border: none;
  border-radius: 40px;
}

.cart-area .quantity .qtybutton {
  position: absolute;
  top: 0;
  left: 0px;
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 28px;
  font-size: 18px;
  cursor: pointer;
  color: #333;
  background: #fafaff;
  border-radius: 30px;
}

.cart-area .cart-wrap .quantity .qtybutton {
  top: 50%;
  left: 5px;
  transform: translateY(-51%);
  -webkit-transform: translateY(-51%);
  -moz-transform: translateY(-51%);
}

.cart-area .cart-wrap .quantity .qtybutton.dec {
  border-left: none;
}

.cart-area .cart-wrap .quantity .qtybutton.inc {
  right: 5px;
  left: auto;
  border-right: none;
}

.cart-area .submit-btn-area {
  padding: 40px;
}

.cart-area .submit-btn-area ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  list-style: none;
}

@media (max-width: 450px) {
  .cart-area .submit-btn-area ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.cart-area .submit-btn-area ul li:last-child {
  margin-left: 20px;
}

@media (max-width: 450px) {
  .cart-area .submit-btn-area ul li:last-child {
    margin-left: 3px;
    margin-top: 5px;
  }
}

.cart-area .submit-btn-area button {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all .3s;
  transition: all .3s;
  outline: none;
  font-weight: 500;
}

.cart-area .submit-btn-area button:hover {
  background: #E5F346;
}

.cart-area .submit-btn-area .theme-btn, .cart-area .submit-btn-area .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.cart-area .submit-btn-area .theme-btn::before, .cart-area .submit-btn-area .view-cart-btn::before {
  display: none;
  position: unset;
}

.cart-area .cart-product-list ul {
  border-top: 1px solid #f0f0f094;
  padding-top: 20px;
  list-style: none;
}

.cart-area .cart-product-list ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0px 30px;
  font-weight: 300;
  font-size: 18px;
  line-height: 21px;
  color: #414141;
  padding-bottom: 30px;
}

.cart-area .cart-product-list ul li.cart-b {
  border-top: 1px solid #f0f0f094;
  border-bottom: 1px solid #f0f0f094;
  color: #adb927;
  padding-top: 30px;
  font-weight: 600;
}

/*===============================
13. wpo-checkout-page-style
================================*/
.wpo-checkout-area .checkout-wrap ul {
  list-style: none;
}

.wpo-checkout-area #open2 {
  display: block;
}

.wpo-checkout-area #open3 {
  display: none;
}

.wpo-checkout-area #open4 {
  display: none;
}

.wpo-checkout-area .create-account p {
  margin-bottom: 15px;
  color: #525252;
}

.wpo-checkout-area .create-account {
  display: none;
  padding: 20px;
}

.wpo-checkout-area .create-account span {
  margin-bottom: 20px;
  display: block;
  color: #525252;
}

.wpo-checkout-area .input-wrap {
  position: relative;
}

.wpo-checkout-area .create-account input {
  width: 100%;
  height: 50px;
  border: 1px solid #ebebeb;
  margin-bottom: 25px;
  padding-left: 20px;
  border-radius: 40px;
}

.wpo-checkout-area .create-account input:focus {
  outline: none;
}

.wpo-checkout-area .create-account button {
  position: absolute;
  right: 0;
  top: 0;
  height: 50px;
  background: #333;
  color: #fff;
  width: 108px;
  border: none;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  cursor: pointer;
  border-top-right-radius: 40px;
  border-bottom-right-radius: 40px;
}

.wpo-checkout-area .create-account button:hover {
  background: #adb927;
}

.wpo-checkout-area .coupon {
  position: relative;
  cursor: pointer;
  cursor: pointer;
  background: #F5F6E4;
}

.wpo-checkout-area .coupon:before {
  position: absolute;
  right: 30px;
  top: 21px;
  content: "\e64b";
  font-family: 'themify';
}

.wpo-checkout-area .coupon span {
  position: absolute;
  right: 30px;
  top: 30px;
  color: #525252;
}

.wpo-checkout-area .coupon label {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 18px;
  color: #525252;
  cursor: pointer;
  padding: 20px 20px;
  border-left: 4px solid #adb927;
  -webkit-transition: all .3s;
  transition: all .3s;
  width: 100%;
}

@media (max-width: 767px) {
  .wpo-checkout-area .coupon label {
    font-size: 15px;
  }
}

.wpo-checkout-area .caupon-wrap {
  background: #fff;
  margin-bottom: 20px;
  -webkit-transition: all .3s;
  transition: all .3s;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
}

.wpo-checkout-area .active-border {
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-checkout-area .s1.active-border .coupon-active label,
.wpo-checkout-area .s3.coupon-2 .coupon-3 label {
  border-left: none;
  border-top: 4px solid #adb927;
  border-bottom: 1px solid #adb927;
}

.wpo-checkout-area .s2 .coupon-3 {
  border-top: 4px solid #adb927;
}

.wpo-checkout-area .s2.coupon-2 .coupon-3 {
  border-top: 0;
}

.wpo-checkout-area .s2 .coupon-3 label {
  border-left: 0;
  border-bottom: 1px solid #ffe5dc;
}

.wpo-checkout-area .s2.coupon-2 .coupon-3 label {
  border-left: 4px solid #adb927;
  border-bottom: 0;
}

.wpo-checkout-area .billing-adress .form-style input,
.wpo-checkout-area .billing-adress .form-style select {
  width: 100%;
  margin-bottom: 15px;
  padding-left: 10px;
  height: 40px;
  background: #fff;
  border-radius: 2px;
  border: none;
  border: 1px solid #ebebeb;
  border-radius: 40px;
}

.wpo-checkout-area .billing-adress .form-style input:focus,
.wpo-checkout-area .billing-adress .form-style select:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-checkout-area .billing-adress .form-style select {
  -webkit-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  -moz-appearance: none;
  background: transparent url(../images/select-icon2.png) no-repeat calc(100% - 15px) center;
  position: relative;
}

.wpo-checkout-area .billing-adress {
  padding: 30px;
}

.wpo-checkout-area .note-area textarea {
  width: 100%;
  height: 150px;
  padding-top: 10px;
  margin-bottom: 0;
  padding-left: 10px;
  border: 1px solid #ebebeb;
  border-radius: 20px;
}

.wpo-checkout-area .note-area textarea:focus {
  outline: none;
}

.wpo-checkout-area .note-area p {
  color: #525252;
}

.wpo-checkout-area .biling-item-2 {
  margin-top: 20px;
}

.wpo-checkout-area .biling-item-2 label.fontsize {
  margin-bottom: 20px;
}

.wpo-checkout-area .biling-item-2 .billing-adress {
  padding: 0;
}

.wpo-checkout-area .payment-name ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-name ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-checkout-area .payment-area form {
  overflow: unset !important;
}

.wpo-checkout-area .payment-name ul li {
  width: 100px;
  height: 60px;
  text-align: center;
  line-height: 60px;
}

.wpo-checkout-area .payment-name ul li input {
  margin-right: 0;
}

.wpo-checkout-area .payment-name ul li {
  margin-right: 15px;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-name ul li {
    margin-bottom: 10px;
  }
}

.wpo-checkout-area .payment-area h2 {
  padding-bottom: 40px;
  margin-bottom: 0;
}

.wpo-checkout-area .payment-select {
  padding: 40px 0;
}

.wpo-checkout-area .payment-select ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-select ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-checkout-area .payment-select ul li {
  margin-right: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #5B5B5B;
}

.wpo-checkout-area .payment-select label {
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-select label {
    font-size: 14px;
  }
}

.wpo-checkout-area .payment-area h2 {
  font-size: 20px;
  color: #878787;
  font-weight: 700;
}

.wpo-checkout-area .payment-area h2 span {
  font-size: 30px;
  color: #ff493c;
}

.wpo-checkout-area .payment-area {
  width: 100%;
}

.wpo-checkout-area label {
  color: #525252;
}

.wpo-checkout-area .payment-name ul li input {
  margin-right: 0;
  position: absolute;
  z-index: -1;
}

.wpo-checkout-area .payment-name ul li input:checked ~ label {
  border: 1px solid #ff493c;
}

.wpo-checkout-area .payment-name label {
  width: 100%;
  border: 1px solid transparent;
}

.wpo-checkout-area .payment-name .visa label {
  border: 1px solid #0057A0;
}

.wpo-checkout-area .payment-name .mas label {
  border: 1px solid #CC0000;
}

.wpo-checkout-area .payment-name .ski label {
  border: 1px solid #691A5F;
}

.wpo-checkout-area .payment-name .pay label {
  border: 1px solid #019CDE;
}

.wpo-checkout-area .payment-option {
  padding: 30px;
}

.wpo-checkout-area .payment-name {
  display: none;
}

.wpo-checkout-area .payment-name.active {
  display: block;
}

.wpo-checkout-area .payment-option.active .payment-name {
  display: none !important;
}

.wpo-checkout-area .payment-area .form-style input,
.wpo-checkout-area .payment-area .form-style select {
  width: 100%;
  margin-bottom: 15px;
  padding-left: 10px;
  height: 40px;
  background: #fff;
  border-radius: 2px;
  border: none;
  border: 1px solid #ebebeb;
  border-radius: 40px;
}

.wpo-checkout-area .payment-area .form-style input:focus,
.wpo-checkout-area .payment-area .form-style select:focus {
  outline: none;
}

.wpo-checkout-area .payment-area .contact-form {
  margin-top: 40px;
}

.wpo-checkout-area .cout-order-area {
  -webkit-box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
}

.wpo-checkout-area .cout-order-area .oreder-item ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 20px 30px;
  font-weight: 300;
  font-size: 18px;
  line-height: 21px;
  color: #414141;
}

.wpo-checkout-area .cout-order-area .oreder-item ul li.s-total {
  font-weight: 400;
}

.wpo-checkout-area .cout-order-area .oreder-item ul .o-header {
  color: #0F0F0F;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 1px solid #ebebeb;
}

.wpo-checkout-area .cout-order-area .oreder-item ul .o-middle {
  border-bottom: 1px solid #ebebeb;
}

.wpo-checkout-area .cout-order-area .oreder-item ul .o-bottom {
  border-top: 1px solid #ebebeb;
  color: #adb927;
  font-weight: 600;
}

.wpo-checkout-area .create-account.active {
  display: block;
}

.wpo-checkout-area .contact-form .form-control {
  -webkit-appearance: auto;
     -moz-appearance: auto;
          appearance: auto;
}

.wpo-checkout-area .contact-form input,
.wpo-checkout-area .contact-form select {
  margin-top: 10px;
}

.wpo-checkout-area .submit-btn-area {
  margin-top: 20px;
}

.wpo-checkout-area .submit-btn-area .theme-btn, .wpo-checkout-area .submit-btn-area .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.wpo-checkout-area .submit-btn-area .theme-btn::before, .wpo-checkout-area .submit-btn-area .view-cart-btn::before {
  display: none;
  position: unset;
}

/*--------------------------------------------------------------
14. wpo-blog-pg-section
--------------------------------------------------------------*/
.wpo-blog-pg-section {
  /*** format-standard ***/
  /*** format-gallery ***/
  /*** format-quote ***/
  /*** format-video ***/
}

.wpo-blog-pg-section .wpo-blog-content .post {
  margin-bottom: 70px;
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .wpo-blog-content .post {
    margin-bottom: 50px;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .wpo-blog-content .post {
    margin-bottom: 40px;
  }
}

.wpo-blog-pg-section .entry-meta {
  list-style: none;
  overflow: hidden;
  margin: 35px 0;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .entry-meta {
    margin: 25px 0;
  }
}

.wpo-blog-pg-section .entry-meta ul {
  list-style: none;
}

.wpo-blog-pg-section .entry-meta ul li {
  font-weight: 400;
  font-size: 14px;
  font-size: 0.93333rem;
  float: left;
  text-transform: uppercase;
  color: #525252;
}

.wpo-blog-pg-section .entry-meta ul li a {
  color: #636893;
}

.wpo-blog-pg-section .entry-meta ul li a:hover {
  color: #adb927;
}

.wpo-blog-pg-section .entry-meta ul li i {
  position: relative;
  margin-right: 3px;
}

.wpo-blog-pg-section .entry-meta ul li i:before {
  font-size: 13px;
}

.wpo-blog-pg-section .entry-meta ul li + li {
  margin-left: 20px;
  padding-left: 20px;
  position: relative;
}

.wpo-blog-pg-section .entry-meta ul li + li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 7px;
  height: 7px;
  content: "";
  background: #E5F346;
  border-radius: 50%;
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li + li:before {
    display: none;
  }
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li + li {
    margin-left: 0;
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .entry-meta ul li {
    font-size: 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li {
    float: none;
    display: block;
    margin-bottom: 5px;
  }
}

.wpo-blog-pg-section .post h3 {
  font-size: 34px;
  font-size: 2.26667rem;
  line-height: 1.2em;
  font-weight: 400;
  margin: -0.27em 0 0.7em;
  font-family: "Epilogue";
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .post h3 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .post h3 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

.wpo-blog-pg-section .post h3 a {
  color: #1C1817;
}

.wpo-blog-pg-section .post h3 a:hover {
  color: #adb927;
}

.wpo-blog-pg-section .post p {
  margin-bottom: 1.5em;
  color: #525252;
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .post p {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.wpo-blog-pg-section .post a.read-more {
  color: #424740;
  text-transform: capitalize;
  font-size: 14px;
}

.wpo-blog-pg-section .post a.read-more:hover {
  color: #adb927;
}

.wpo-blog-pg-section .entry-media img {
  width: 100%;
}

.wpo-blog-pg-section .format-standard,
.wpo-blog-pg-section .format-quote {
  background-color: #f9f9f9;
  padding: 25px 35px 45px;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .format-standard,
  .wpo-blog-pg-section .format-quote {
    padding: 25px 20px 45px;
  }
}

.wpo-blog-pg-section .format-standard {
  position: relative;
}

.wpo-blog-pg-section .format-standard:before {
  position: absolute;
  right: 20px;
  top: 10px;
  content: "\f10a";
  font-family: "flaticon";
  font-size: 90px;
  line-height: 80px;
  color: #f1f1f1;
}

.wpo-blog-pg-section .format-gallery {
  position: relative;
}

.wpo-blog-pg-section .format-gallery .owl-controls {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-]:hover {
  background: #E5F346;
  color: #1C1817;
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-] {
  background: #fff;
  width: 50px;
  height: 50px;
  line-height: 58px;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border: 0;
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-prev,
.wpo-blog-pg-section .format-gallery .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-prev {
  left: 15px;
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-next {
  right: 15px;
}

.wpo-blog-pg-section .format-quote {
  text-align: center;
  padding: 80px 60px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .format-quote {
    padding: 40px 20px;
  }
}

.wpo-blog-pg-section .format-quote p {
  margin-bottom: 0;
}

.wpo-blog-pg-section .format-quote:before {
  font-family: "Flaticon";
  content: "\f120";
  font-size: 250px;
  font-size: 16.66667rem;
  color: #ecf3fb;
  margin-left: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.wpo-blog-pg-section .format-quote h3,
.wpo-blog-pg-section .format-quote p {
  position: relative;
}

.wpo-blog-pg-section .format-video .video-holder {
  position: relative;
  text-align: center;
}

.wpo-blog-pg-section .format-video .video-holder:before {
  content: "";
  background-color: #163967;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  opacity: 0.5;
}

.wpo-blog-pg-section .format-video .video-holder:hover:before {
  opacity: 0.8;
}

.wpo-blog-pg-section .format-video .video-holder a {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 63px;
  height: 63px;
  line-height: 70px;
  background: #fff;
  border-radius: 50%;
  z-index: 1;
}

.wpo-blog-pg-section .format-video .video-holder a:after {
  position: absolute;
  left: -12px;
  top: -12px;
  width: 140%;
  height: 140%;
  background: rgba(255, 255, 255, 0.3);
  content: "";
  -webkit-animation: spineer 2s infinite;
  animation: spineer 2s infinite;
  border-radius: 50%;
  z-index: -1;
}

.wpo-blog-pg-section .format-video .video-holder a:before {
  content: "";
  width: 0px;
  height: 0px;
  border-top: 9px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #2f426b;
  position: absolute;
  left: 52%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.wpo-blog-pg-section .format-video .video-holder .fi:before {
  font-size: 20px;
  font-size: 1.33333rem;
  color: #E5F346;
}

@media screen and (min-width: 1200px) {
  .blog-pg-left-sidebar .blog-sidebar {
    padding-right: 45px;
    padding-left: 0;
  }
}

@media screen and (min-width: 1200px) {
  .blog-pg-fullwidth .wpo-blog-content {
    padding: 0;
  }
}

@-webkit-keyframes save-the-date-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(0.95, 0.95, 0.95);
    transform: scale3d(0.95, 0.95, 0.95);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes save-the-date-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(0.95, 0.95, 0.95);
    transform: scale3d(0.95, 0.95, 0.95);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

/*--------------------------------------------------------------
15. wpo-blog-single-section
--------------------------------------------------------------*/
.wpo-blog-single-section {
  /*** tag-share ***/
  /*** author-box ***/
  /*** more-posts ***/
  /*** comments area ***/
  /*** comment-respond ***/
}

@media (min-width: 1440px) {
  .wpo-blog-single-section .container {
    max-width: 1320px;
  }
}

.wpo-blog-single-section .entry-meta {
  list-style: none;
  overflow: hidden;
  margin: 35px 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .entry-meta {
    margin: 25px 0;
  }
}

.wpo-blog-single-section .entry-meta ul {
  list-style: none;
}

.wpo-blog-single-section .entry-meta ul li {
  font-weight: 500;
  font-size: 14px;
  font-size: 0.93333rem;
  float: left;
  text-transform: uppercase;
  color: #525252;
}

.wpo-blog-single-section .entry-meta ul li a {
  color: #636893;
}

.wpo-blog-single-section .entry-meta ul li a:hover {
  color: #adb927;
}

.wpo-blog-single-section .entry-meta ul li i {
  position: relative;
  top: 2px;
  margin-right: 3px;
}

.wpo-blog-single-section .entry-meta ul li + li {
  margin-left: 20px;
  padding-left: 20px;
  position: relative;
}

.wpo-blog-single-section .entry-meta ul li + li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 7px;
  height: 7px;
  content: "";
  background: #E5F346;
  border-radius: 50%;
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li + li:before {
    display: none;
  }
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li + li {
    margin-left: 0;
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .entry-meta ul li {
    font-size: 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li {
    float: none;
    display: block;
    margin-bottom: 5px;
  }
}

.wpo-blog-single-section .entry-media img {
  width: 100%;
}

.wpo-blog-single-section .post h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  margin: -0.22em 0 0.7em;
  line-height: 1.3em;
  font-family: "Epilogue";
  font-weight: 400;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .post h2 {
    font-size: 30px;
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post h2 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-blog-single-section .post p {
  margin-bottom: 1.5em;
  color: #525252;
}

.wpo-blog-single-section .post h3 {
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1.3em;
  margin: 1.8em 0 1em;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .post h3 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post h3 {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .post blockquote {
  background-color: #f9f9f9;
  color: #233d62;
  font-size: 18px;
  padding: 65px;
  margin-top: 60px;
  margin-bottom: 40px;
  border: 0;
  text-align: center;
  position: relative;
  font-weight: 400;
  line-height: 35px;
  padding-bottom: 45px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post blockquote {
    padding: 55px 25px;
  }
}

.wpo-blog-single-section .post blockquote:before {
  font-family: "Flaticon";
  content: "\f10a";
  font-size: 20px;
  font-size: 1.33333rem;
  color: rgba(255, 255, 255, 0.5);
  position: absolute;
  left: 50%;
  top: -30px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  line-height: 60px;
  border: 2px solid #F5F6E4;
  background: #fff;
  color: #adb927;
  border-radius: 50%;
}

.wpo-blog-single-section .post .gallery {
  overflow: hidden;
  margin: 40px -7.5px 0;
}

.wpo-blog-single-section .post .gallery > div {
  width: calc(50% - 15px);
  float: left;
  margin: 0 7.5px 15px;
}

.wpo-blog-single-section .post .gallery img {
  width: 100%;
}

.wpo-blog-single-section .tag-share,
.wpo-blog-single-section .tag-share-s2 {
  border-bottom: 1px solid #f9f9f9;
  margin: 75px 0 0;
  padding-bottom: 30px;
  color: #1C1817;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share,
  .wpo-blog-single-section .tag-share-s2 {
    margin-top: 40px;
  }
}

.wpo-blog-single-section .tag-share ul,
.wpo-blog-single-section .tag-share-s2 ul {
  list-style: none;
  display: inline-block;
  overflow: hidden;
}

.wpo-blog-single-section .tag-share ul li,
.wpo-blog-single-section .tag-share-s2 ul li {
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share ul li,
  .wpo-blog-single-section .tag-share-s2 ul li {
    margin: 2px;
  }
}

.wpo-blog-single-section .tag-share ul > li + li,
.wpo-blog-single-section .tag-share-s2 ul > li + li {
  margin-left: 10px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share ul > li + li,
  .wpo-blog-single-section .tag-share-s2 ul > li + li {
    margin: 2px;
  }
}

.wpo-blog-single-section .tag-share .tag,
.wpo-blog-single-section .tag-share-s2 .tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-blog-single-section .tag-share .tag > span,
.wpo-blog-single-section .tag-share-s2 .tag > span {
  font-family: "Epilogue";
  color: #1C1817;
  font-weight: 400;
  display: inline-block;
  padding-right: 15px;
  text-transform: uppercase;
}

.wpo-blog-single-section .tag-share .tag ul,
.wpo-blog-single-section .tag-share-s2 .tag ul {
  list-style: none;
  position: relative;
}

.wpo-blog-single-section .tag-share .tag li,
.wpo-blog-single-section .tag-share-s2 .tag li {
  position: relative;
}

.wpo-blog-single-section .tag-share .tag a,
.wpo-blog-single-section .tag-share-s2 .tag a {
  font-size: 13px;
  font-size: 0.86667rem;
  display: inline-block;
  padding: 5px 18px;
  color: #1C1817;
  background: #F5F6E4;
  border-radius: 5px;
  font-weight: 400;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share .tag a,
  .wpo-blog-single-section .tag-share-s2 .tag a {
    font-size: 13px;
    font-size: 0.86667rem;
  }
}

.wpo-blog-single-section .tag-share .tag a:hover,
.wpo-blog-single-section .tag-share-s2 .tag a:hover {
  color: #1C1817;
}

.wpo-blog-single-section .tag-share-s2 {
  margin: 0;
  margin-top: 30px;
  border-bottom: 0;
}

.wpo-blog-single-section .tag-share-s2 .tag a {
  padding: 0;
  font-size: 16px;
  font-size: 1.06667rem;
  text-transform: capitalize;
  background: none;
  text-decoration: underline;
  color: #424740;
}

.wpo-blog-single-section .author-box {
  margin: 35px 0 60px;
}

.wpo-blog-single-section .author-box .author-avatar {
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .author-box .author-avatar {
    float: none;
  }
}

.wpo-blog-single-section .author-box .author-avatar img {
  border-radius: 50%;
}

.wpo-blog-single-section .author-box .author-content {
  display: block;
  overflow: hidden;
  padding-left: 25px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .author-box .author-content {
    padding: 0;
    margin: 15px 0 0 0;
  }
}

.wpo-blog-single-section .author-box .author-content p {
  margin-bottom: 20px;
  color: #525252;
}

.wpo-blog-single-section .author-box .author-name {
  font-family: "Epilogue";
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: 400;
  display: inline-block;
  margin-bottom: 10px;
  color: #1C1817;
}

.wpo-blog-single-section .author-box .social-link {
  display: inline-block;
  list-style: none;
}

.wpo-blog-single-section .author-box .social-link li {
  float: left;
  margin-right: 12px;
}

.wpo-blog-single-section .author-box .social-link a {
  display: block;
  font-size: 13px;
  font-size: 0.86667rem;
  color: #1C1817;
}

.wpo-blog-single-section .author-box .social-link a:hover {
  color: #adb927;
}

.wpo-blog-single-section .more-posts {
  overflow: hidden;
  border: 1px solid #DADADA;
  padding: 0 25px;
}

.wpo-blog-single-section .more-posts > div {
  width: 50%;
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts > div {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .more-posts > div > a {
  display: inline-block;
}

.wpo-blog-single-section .more-posts .previous-post,
.wpo-blog-single-section .more-posts .next-post {
  padding: 40px 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post,
  .wpo-blog-single-section .more-posts .next-post {
    padding: 25px 15px !important;
  }
}

.wpo-blog-single-section .more-posts .next-post {
  text-align: right;
  border-left: 1px solid #DADADA;
  padding-left: 15px;
  padding-right: 5px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post {
    border-left: 0;
    text-align: left;
    border-top: 1px solid #DADADA;
  }
}

.wpo-blog-single-section .more-posts .next-post .post-control-link {
  padding-right: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post .post-control-link {
    padding-right: 0;
  }
}

.wpo-blog-single-section .more-posts .next-post .post-control-link:before {
  font-family: "themify";
  content: "\e628";
  position: absolute;
  right: 0;
  top: 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post .post-control-link:before {
    display: none;
  }
}

.wpo-blog-single-section .more-posts .previous-post {
  padding-right: 15px;
  padding-left: 5px;
}

.wpo-blog-single-section .more-posts .previous-post .post-control-link {
  padding-left: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post .post-control-link {
    padding-left: 0;
  }
}

.wpo-blog-single-section .more-posts .previous-post .post-control-link:before {
  font-family: "themify";
  content: "\e629";
  position: absolute;
  left: 0;
  top: 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post .post-control-link:before {
    display: none;
  }
}

.wpo-blog-single-section .more-posts .previous-post > a > span,
.wpo-blog-single-section .more-posts .next-post > a > span {
  display: block;
}

.wpo-blog-single-section .more-posts .post-control-link {
  font-size: 14px;
  font-size: 0.93333rem;
  color: #424740;
  text-transform: uppercase;
  font-weight: 400;
  letter-spacing: 2px;
}

.wpo-blog-single-section .more-posts .post-name {
  font-family: "Epilogue";
  font-size: 18px;
  font-size: 1.2rem;
  color: #1C1817;
  margin: 0.7em 0 0;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .more-posts .post-name {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

.wpo-blog-single-section .more-posts a:hover .post-control-link {
  color: #adb927;
}

.wpo-blog-single-section .comments-area {
  margin-top: 70px;
}

.wpo-blog-single-section .comments-area li > div {
  border-bottom: 1px solid #ebebeb;
  padding: 35px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comments-area li > div {
    padding: 35px 25px;
  }
}

.wpo-blog-single-section .comments-area ol {
  list-style-type: none;
  padding-left: 0;
}

.wpo-blog-single-section .comments-area ol ul {
  padding-left: 30px;
  list-style-type: none;
}

.wpo-blog-single-section .comments-area ol > li:last-child div {
  border-bottom: 0;
}

.wpo-blog-single-section .comments-area .comments-title {
  font-size: 22px;
  font-size: 1.46667rem;
  font-weight: 500;
  margin: 0 0 1em;
  text-transform: uppercase;
  letter-spacing: 3px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comments-area .comments-title {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .comments-area li > div {
  position: relative;
}

.wpo-blog-single-section .comments-area .comment-theme {
  position: absolute;
  left: 35px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comment-theme {
    position: static;
  }
}

.wpo-blog-single-section .comments-area .comment-theme img {
  border-radius: 50%;
}

.wpo-blog-single-section .comments-area .comment-main-area {
  padding-left: 100px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comment-main-area {
    padding-left: 0;
    margin-top: 25px;
  }
}

.wpo-blog-single-section .comments-area .comment-main-area p {
  margin-bottom: 20px;
  color: #525252;
}

.wpo-blog-single-section .comments-area .comments-meta h4 {
  font-family: "Epilogue";
  font-size: 18px;
  font-size: 1.2rem;
  color: #1C1817;
  font-weight: bold;
  margin: 0 0 1em;
}

.wpo-blog-single-section .comments-area .comments-meta h4 span {
  font-size: 15px;
  font-size: 1rem;
  color: #424740;
  font-weight: normal;
  text-transform: none;
  display: inline-block;
  padding-left: 5px;
  font-family: "Sora", sans-serif;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comments-meta h4 span {
    padding-left: 0;
  }
}

.wpo-blog-single-section .comments-area .comment-reply-link {
  font-family: "Epilogue";
  font-size: 14px;
  font-size: 0.93333rem;
  font-weight: 400;
  color: #1C1817;
  text-align: center;
  border-radius: 50px;
  display: inline-block;
  font-family: "Sora", sans-serif;
  text-decoration: underline;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wpo-blog-single-section .comments-area .comment-reply-link:hover {
  color: #adb927;
}

.wpo-blog-single-section .comment-respond {
  margin-top: 70px;
}

.wpo-blog-single-section .comment-respond .comment-reply-title {
  font-size: 22px;
  font-size: 1.46667rem;
  margin: 0 0 1.5em;
  text-transform: uppercase;
  letter-spacing: 2px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond .comment-reply-title {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .comment-respond form input,
.wpo-blog-single-section .comment-respond form textarea {
  background-color: #fff;
  width: 100%;
  height: 55px;
  border: 1px solid #a4adbe;
  padding: 6px 15px;
  margin-bottom: 15px;
  outline: 0;
  border-radius: 30px;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.wpo-blog-single-section .comment-respond form input:focus,
.wpo-blog-single-section .comment-respond form textarea:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #E5F346;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond form input,
  .wpo-blog-single-section .comment-respond form textarea {
    height: 40px;
  }
}

.wpo-blog-single-section .comment-respond form textarea {
  height: 220px;
  padding: 15px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond form textarea {
    height: 150px;
  }
}

.wpo-blog-single-section .comment-respond .form-inputs {
  overflow: hidden;
}

.wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(1) {
  width: 49%;
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(1) {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(2) {
  width: 49%;
  float: right;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(2) {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .comment-respond .form-submit input {
  font-family: "Epilogue";
  max-width: 180px;
  background-color: #E5F346;
  color: #1C1817;
  margin-bottom: 0;
  border: 0;
  outline: 0;
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 2px;
  border-radius: 30px;
  line-height: 42px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond .form-submit input {
    line-height: 32px;
  }
}

.wpo-blog-single-section .comment-respond .form-submit input:hover {
  background-color: #dff01b;
}

@media screen and (min-width: 1200px) {
  .wpo-blog-single-left-sidebar-section .blog-sidebar {
    padding-right: 45px;
    padding-left: 0;
  }
}

/*--------------------------------------------------------------
16. wpo-contact-pg-section
--------------------------------------------------------------*/
.wpo-contact-pg-section .row {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.wpo-contact-pg-section .wpo-contact-title {
  max-width: 440px;
  margin: 0 auto;
  text-align: center;
  margin-bottom: 50px;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section .wpo-contact-title {
    margin-bottom: 30px;
  }
}

.wpo-contact-pg-section .wpo-contact-title h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  font-weight: 700;
  margin-bottom: 20px;
}

@media (max-width: 575px) {
  .wpo-contact-pg-section .wpo-contact-title h2 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-contact-pg-section form input,
.wpo-contact-pg-section form select,
.wpo-contact-pg-section form textarea {
  background: #fff;
  width: 100%;
  height: 50px;
  border-radius: 0px;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  padding-left: 25px;
  border: 0;
}

@media (max-width: 991px) {
  .wpo-contact-pg-section form input,
  .wpo-contact-pg-section form select,
  .wpo-contact-pg-section form textarea {
    height: 45px;
  }
}

.wpo-contact-pg-section form input:focus,
.wpo-contact-pg-section form select:focus,
.wpo-contact-pg-section form textarea:focus {
  border-color: #E5F346;
  background: #fff;
}

.wpo-contact-pg-section form textarea {
  height: 180px;
  padding-top: 15px;
}

.wpo-contact-pg-section form {
  margin: 0 -15px;
  overflow: hidden;
}

.wpo-contact-pg-section form ::-webkit-input-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form :-moz-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form ::-moz-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form :-ms-input-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form select {
  display: inline-block;
  color: #a9a9a9;
  cursor: pointer;
  opacity: 1;
  padding: 6px 25px;
  font-size: 15px;
  font-size: 1rem;
  -webkit-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  -moz-appearance: none;
  background: #fff url(../images/select-icon2.png) no-repeat calc(100% - 15px) center;
  position: relative;
}

.wpo-contact-pg-section form select:focus {
  background: #fff url(../images/select-icon2.png) no-repeat calc(100% - 15px) center;
}

.wpo-contact-pg-section form .submit-area {
  text-align: center;
  width: 100%;
  margin-bottom: 10px;
  margin-left: 0;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section form .submit-area {
    margin-bottom: 0;
  }
}

.wpo-contact-pg-section form .submit-area .theme-btn, .wpo-contact-pg-section form .submit-area .view-cart-btn {
  border-radius: 0px;
  font-family: "Sora", sans-serif;
  font-size: 16px;
}

.wpo-contact-pg-section form .submit-area .theme-btn:after, .wpo-contact-pg-section form .submit-area .view-cart-btn:after {
  border-radius: 0px;
}

.wpo-contact-pg-section form > div {
  width: calc(50% - 30px);
  float: left;
  margin: 0 15px 25px;
}

@media (max-width: 600px) {
  .wpo-contact-pg-section form > div {
    width: calc(100% - 25px);
    float: none;
  }
}

.wpo-contact-pg-section form .fullwidth {
  width: calc(100% - 25px);
  float: none;
  clear: both;
}

.wpo-contact-pg-section .office-info {
  padding-top: 50px;
  margin-left: 50px;
}

@media (max-width: 991px) {
  .wpo-contact-pg-section .office-info {
    margin-left: 0;
  }
}

.wpo-contact-pg-section .office-info .office-info-item {
  padding: 20px;
  border: 1px solid #EFEFEF;
  margin-bottom: 15px;
}

.wpo-contact-pg-section .office-info .office-info-item:last-child {
  margin-bottom: 0;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-icon {
  float: left;
  overflow: hidden;
  margin-right: 20px;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-icon .icon .fi:before {
  font-size: 30px;
  color: #adb927;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-text {
  overflow: hidden;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-text h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #424740;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #1C1817;
}

.wpo-contact-pg-section .wpo-contact-form-area {
  padding: 50px;
  background: #F5F6E4;
  padding-bottom: 0;
  position: relative;
  z-index: 99;
  margin-top: -150px;
  z-index: 1;
  overflow: hidden;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section .wpo-contact-form-area {
    padding: 30px;
    padding-top: 50px;
  }
}

@media (max-width: 575px) {
  .wpo-contact-pg-section .wpo-contact-form-area {
    margin-top: 0;
    margin-top: 50px;
  }
}

.wpo-contact-pg-section .wpo-contact-form-area:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.wpo-contact-map-section {
  padding: 100px 80px;
  padding-bottom: 0;
}

@media (max-width: 1600px) {
  .wpo-contact-map-section {
    padding: 100px 30px;
    padding-bottom: 0;
  }
}

@media (max-width: 1199px) {
  .wpo-contact-map-section {
    padding: 100px 0px;
    padding-bottom: 0;
  }
}

@media (max-width: 991px) {
  .wpo-contact-map-section {
    padding: 90px 0px;
    padding-bottom: 0;
  }
}

.wpo-contact-map-section .wpo-contact-map {
  height: 550px;
}

.wpo-contact-map-section iframe {
  width: 100%;
  height: 100%;
  border: 0;
  outline: 0;
}

.wpo-contact-map-section h2.hidden {
  display: none;
}

/*--------------------------------------------------------------
17. wpo-faq-page
--------------------------------------------------------------*/
.wpo-faq-section .wpo-section-title {
  text-align: center;
}

.wpo-faq-section .wpo-section-title h2 {
  display: inline-block;
}

.wpo-faq-section .accordion-item {
  border: 0;
  border: 1px solid #1C1817;
  padding: 0;
  margin-bottom: 20px;
}

.wpo-faq-section .accordion-item .accordion-body {
  background: #F5F6E4;
  border-top: 1px solid #1C1817;
}

.wpo-faq-section .accordion-item .accordion-body p {
  color: #424740;
  margin-bottom: 0;
}

.wpo-faq-section .accordion-item button {
  padding: 18px;
  padding-top: 20px;
  border: 0;
  border-radius: 0;
  font-size: 17px;
  color: #1C1817;
  text-align: left;
  font-family: "Epilogue";
  text-transform: uppercase;
  font-weight: 500;
  background: #E5F346;
  line-height: 27px;
}

@media (max-width: 991px) {
  .wpo-faq-section .accordion-item button {
    font-size: 15px;
  }
}

.wpo-faq-section .accordion-item button::after {
  background: none;
  font-family: "themify";
  content: "\e622";
  font-size: 15px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  font-weight: 700;
  position: relative;
  top: -7px;
}

.wpo-faq-section .accordion-item button.collapsed {
  background: #fff;
}

.wpo-faq-section .accordion-item button.collapsed::after {
  content: "\e61a";
}

.wpo-faq-section .accordion-item button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
  border-color: #1C1817;
}

.wpo-faq-section .accordion-item .accordion-collapse {
  border: 0;
}

.wpo-faq-section .accordion-button {
  background: transparent;
}

/*-----------------------------------------------------
#4.1 faq section
------------------------------------------------------*/
.question-area {
  background: #F5F6E4;
  padding-top: 100px;
  position: relative;
  z-index: 1;
}

@media (max-width: 991px) {
  .question-area {
    padding-top: 80px;
  }
}

@media (max-width: 767px) {
  .question-area {
    padding-top: 60px;
  }
}

.question-area:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../images/noice.png);
  z-index: -1;
}

.question-area .wpo-section-title {
  text-align: center;
}

.question-area .wpo-section-title h2 {
  display: inline-block;
}

.question-touch {
  max-width: 570px;
  margin: auto;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
  padding: 50px;
  text-align: center;
  padding-top: 60px;
  background: #fff;
  border-radius: 5px;
}

@media (max-width: 590px) {
  .question-touch {
    padding: 15px;
  }
}

@media (max-width: 991px) {
  .faq-pb {
    margin-top: 15px;
  }
}

.question-touch h2 {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  margin-top: 0;
  margin-bottom: 35px;
}

.question-touch .half-col {
  width: 100%;
}

.question-touch input,
.question-touch textarea {
  width: 100%;
  height: 50px;
  border: none;
  outline: none;
  background: none;
  border-radius: 0;
  border-bottom: 1px solid #ddd;
  border-top: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin-bottom: 22px;
  padding: 10px;
}

.question-touch input:focus,
.question-touch textarea:focus {
  outline: none;
  background: none;
  border-radius: 0;
  border-bottom: 1px solid #eeeeee;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.question-touch textarea {
  height: 160px;
}

.question-touch ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #888;
  font-size: 14px;
}

.question-touch ::-moz-placeholder {
  /* Firefox 19+ */
  color: #888;
  font-size: 14px;
}

.question-touch :-ms-input-placeholder {
  /* IE 10+ */
  color: #888;
  font-size: 14px;
}

.question-touch :-moz-placeholder {
  /* Firefox 18- */
  color: #888;
  font-size: 14px;
}

.question-touch .theme-btn, .question-touch .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.question-touch .theme-btn::before, .question-touch .view-cart-btn::before {
  display: none;
  position: unset;
}

/*--------------------------------------------------------------
18. error-404-section
--------------------------------------------------------------*/
.error-404-section {
  text-align: center;
}

.error-404-section .error {
  max-width: 600px;
  margin: 0 auto;
}

.error-404-section .error-message {
  margin: 0 auto;
  margin-top: 50px;
  max-width: 700px;
}

@media (max-width: 991px) {
  .error-404-section .error-message {
    margin-top: 50px;
    padding: 0 100px;
  }
}

@media (max-width: 767px) {
  .error-404-section .error-message {
    padding: 0;
  }
}

.error-404-section .error-message h3 {
  font-size: 30px;
  font-size: 2rem;
  margin: 0 0 0.8em;
}

.error-404-section .error-message p {
  margin-bottom: 1.8em;
  color: #525252;
}

.error-404-section .error-message .theme-btn, .error-404-section .error-message .view-cart-btn {
  padding: 17px 40px;
  border: none;
  background: #E5F346;
  color: #1C1817;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.error-404-section .error-message .theme-btn::before, .error-404-section .error-message .view-cart-btn::before {
  border: 1px dashed #E5F346;
}
/*# sourceMappingURL=style.css.map */